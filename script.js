// Ubuntu Style JavaScript
console.log('Ubuntu script loading...');

document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing Ubuntu interface...');

    // Window management system
    let activeWindows = new Set();
    let windowZIndex = 100;
    let draggedWindow = null;
    let dragOffset = { x: 0, y: 0 };

    // Navigation functionality for both Ubuntu menu and Mac dock
    const navItems = document.querySelectorAll('[data-view]');

    // Handle navigation clicks - open windows
    navItems.forEach(item => {
        item.addEventListener('click', function() {
            const targetApp = this.getAttribute('data-view');
            openWindow(targetApp);
        });
    });

    // Handle action buttons that open other windows
    document.addEventListener('click', function(e) {
        if (e.target.hasAttribute('data-open')) {
            const targetApp = e.target.getAttribute('data-open');
            openWindow(targetApp);
        }
    });

    // Open window function
    function openWindow(appName) {
        console.log('Opening window for app:', appName);
        const window = document.getElementById(appName + '-window');
        if (!window) {
            console.error('Window not found:', appName + '-window');
            return;
        }

        console.log('Window found:', window);

        // If window is already open, just bring it to front
        if (activeWindows.has(appName)) {
            console.log('Window already open, bringing to front');
            bringToFront(window);
            return;
        }

        // Show window
        console.log('Showing window');
        window.classList.add('active', 'opening');
        window.style.zIndex = ++windowZIndex;
        activeWindows.add(appName);

        // Setup controls for this window
        const windowControls = window.querySelectorAll('.ubuntu-control');
        windowControls.forEach(control => {
            control.removeEventListener('click', handleControlClick);
            control.addEventListener('click', handleControlClick);
        });

        // Taskbar removed

        // Apply current theme to new window
        if (typeof themeSystem !== 'undefined' && themeSystem.currentTheme) {
            const theme = themeSystem.themes[themeSystem.currentTheme];
            if (theme) {
                window.style.background = theme.window;
                window.style.color = theme.windowText;

                const header = window.querySelector('.ubuntu-header');
                if (header) {
                    header.style.background = theme.windowHeader;
                    header.style.color = theme.windowHeaderText;

                    const windowTitle = header.querySelector('.window-title');
                    if (windowTitle) {
                        windowTitle.style.color = theme.windowHeaderText;
                    }
                }

                // Apply theme to window content
                const windowContent = window.querySelector('.window-content');
                if (windowContent) {
                    windowContent.style.color = theme.windowText;
                }

                // Apply theme to cards and content
                const cards = window.querySelectorAll('.ubuntu-card');
                cards.forEach(card => {
                    card.style.color = theme.windowText;

                    const headings = card.querySelectorAll('h1, h2, h3, h4, h5, h6');
                    headings.forEach(heading => {
                        if (theme.isDark) {
                            heading.style.color = 'white';
                        } else {
                            heading.style.color = theme.windowText;
                        }
                    });

                    const paragraphs = card.querySelectorAll('p, .contact-item, .solution-info p, .card-content p');
                    paragraphs.forEach(p => {
                        if (theme.isDark) {
                            p.style.color = 'rgba(255, 255, 255, 0.8)';
                        } else {
                            p.style.color = '#666';
                        }
                    });
                });
            }
        }

        // Remove opening animation class
        setTimeout(() => {
            window.classList.remove('opening');
        }, 300);

        // Update dock active state
        updateDockActiveState();
    }

    // Close window function
    function closeWindow(appName) {
        const window = document.getElementById(appName + '-window');
        if (!window) return;

        window.classList.add('closing');

        setTimeout(() => {
            window.classList.remove('active', 'closing');
            activeWindows.delete(appName);
            // Taskbar removed
            updateDockActiveState();
        }, 300);
    }

    // Minimize window function
    function minimizeWindow(appName) {
        const window = document.getElementById(appName + '-window');
        if (!window) return;

        window.classList.add('minimized');
        window.classList.remove('active');

        // Taskbar removed
    }

    // Maximize/restore window function
    function toggleMaximize(appName) {
        const window = document.getElementById(appName + '-window');
        if (!window) return;

        const isMaximized = window.classList.contains('maximized');
        window.classList.toggle('maximized');

        // 更新最大化按钮图标
        const maximizeBtn = window.querySelector('.ubuntu-control.maximize');
        if (maximizeBtn) {
            if (isMaximized) {
                // 从最大化状态恢复
                maximizeBtn.textContent = '□';
                maximizeBtn.title = '最大化';
            } else {
                // 最大化
                maximizeBtn.textContent = '❐';
                maximizeBtn.title = '还原';
            }
        }

        // 确保窗口在最前面
        bringToFront(window);
    }

    // Bring window to front
    function bringToFront(window) {
        window.style.zIndex = ++windowZIndex;

        // Taskbar removed
    }
    
    // Window controls functionality - Direct event binding
    function setupWindowControls() {
        console.log('Setting up window controls...');

        // Get all control buttons
        const allControls = document.querySelectorAll('.ubuntu-control');
        console.log('Found controls:', allControls.length);

        allControls.forEach((control, index) => {
            console.log(`Setting up control ${index}:`, control.getAttribute('data-action'));

            // Remove any existing listeners
            control.removeEventListener('click', handleControlClick);

            // Add click listener
            control.addEventListener('click', handleControlClick);
        });
    }

    function handleControlClick(e) {
        e.preventDefault();
        e.stopPropagation();

        const control = e.currentTarget;
        const window = control.closest('.app-window');

        if (!window) {
            console.error('Window not found for control');
            return;
        }

        const appName = window.getAttribute('data-app');
        const action = control.getAttribute('data-action');

        console.log('Control clicked:', action, 'for app:', appName);

        // Visual feedback
        control.style.transform = 'scale(0.9)';
        setTimeout(() => {
            control.style.transform = 'scale(1)';
        }, 150);

        // Execute action
        switch(action) {
            case 'close':
                console.log('Closing window:', appName);
                closeWindow(appName);
                break;
            case 'minimize':
                console.log('Minimizing window:', appName);
                minimizeWindow(appName);
                break;
            case 'maximize':
                console.log('Toggling maximize for window:', appName);
                toggleMaximize(appName);
                break;
            default:
                console.error('Unknown action:', action);
        }
    }

    // Setup controls after DOM is ready
    setupWindowControls();

    // Taskbar removed - no longer needed

    function updateDockActiveState() {
        // Update dock items to show which apps are open
        document.querySelectorAll('.dock-item').forEach(item => {
            const appName = item.getAttribute('data-view');
            if (activeWindows.has(appName)) {
                item.classList.add('active');
            } else {
                item.classList.remove('active');
            }
        });
    }
    
    // Ubuntu button hover effects
    const ubuntuButtons = document.querySelectorAll('.ubuntu-btn');
    
    ubuntuButtons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });
        
        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
        
        button.addEventListener('click', function() {
            // Click animation
            this.style.transform = 'translateY(0) scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'translateY(-2px) scale(1)';
            }, 150);
        });
    });
    
    // Card hover effects
    const cards = document.querySelectorAll('.ubuntu-card');
    
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.boxShadow = '0 8px 24px rgba(0, 0, 0, 0.15)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)';
        });
    });
    
    // Window dragging functionality
    document.addEventListener('mousedown', function(e) {
        const windowHeader = e.target.closest('.window-header');
        if (windowHeader && !e.target.classList.contains('control')) {
            const window = windowHeader.closest('.app-window');
            if (window.classList.contains('maximized')) return; // Can't drag maximized windows

            draggedWindow = window;
            const rect = window.getBoundingClientRect();
            dragOffset.x = e.clientX - rect.left;
            dragOffset.y = e.clientY - rect.top;

            window.classList.add('dragging');
            bringToFront(window);

            e.preventDefault();
        }
    });

    document.addEventListener('mousemove', function(e) {
        if (draggedWindow) {
            const x = e.clientX - dragOffset.x;
            const y = e.clientY - dragOffset.y;

            // Constrain to viewport
            const maxX = window.innerWidth - draggedWindow.offsetWidth;
            const maxY = window.innerHeight - draggedWindow.offsetHeight;

            draggedWindow.style.left = Math.max(0, Math.min(x, maxX)) + 'px';
            draggedWindow.style.top = Math.max(32, Math.min(y, maxY)) + 'px'; // 32px for top bar
        }
    });

    document.addEventListener('mouseup', function() {
        if (draggedWindow) {
            draggedWindow.classList.remove('dragging');
            draggedWindow = null;
        }
    });

    // Window click to bring to front
    document.addEventListener('mousedown', function(e) {
        const window = e.target.closest('.app-window');
        if (window) {
            bringToFront(window);
        }
    });

    // Ubuntu-style dock - no magnification effects
    // Labels are controlled by CSS hover states

    // Task Manager functionality - Ubuntu Task Overview
    const taskManagerButton = document.querySelector('.task-manager-button');
    let isOverviewMode = false;
    let overviewContainer = null;

    // Create overview container
    function createOverviewContainer() {
        if (overviewContainer) return;

        overviewContainer = document.createElement('div');
        overviewContainer.className = 'overview-container';
        overviewContainer.innerHTML = `
            <div class="overview-header">
                <h2>任务管理器</h2>
                <p>点击窗口切换到该应用，按ESC键退出</p>
            </div>
            <div class="overview-grid" id="overview-grid"></div>
        `;
        document.body.appendChild(overviewContainer);
    }

    taskManagerButton.addEventListener('click', function() {
        toggleTaskOverview();
    });

    function toggleTaskOverview() {
        const desktop = document.querySelector('.ubuntu-desktop');
        const dock = document.querySelector('.mac-dock');
        // Taskbar removed
        const allWindows = document.querySelectorAll('.app-window');

        isOverviewMode = !isOverviewMode;

        if (isOverviewMode) {
            // Enter overview mode
            taskManagerButton.classList.add('active');
            desktop.classList.add('overview-mode');

            // Hide all normal windows
            allWindows.forEach(window => {
                window.style.display = 'none';
            });

            // Create overview container if not exists
            createOverviewContainer();

            // Show overview
            overviewContainer.classList.add('active');

            // Populate overview grid
            populateOverviewGrid();

            // Hide dock in overview
            if (dock) dock.style.opacity = '0.2';

        } else {
            exitOverviewMode();
        }
    }

    function populateOverviewGrid() {
        const grid = document.getElementById('overview-grid');

        // Get all windows that are in activeWindows set (opened windows)
        const openWindows = [];
        activeWindows.forEach(appName => {
            const window = document.getElementById(appName + '-window');
            if (window) {
                openWindows.push(window);
            }
        });

        console.log('Active windows:', activeWindows);
        console.log('Found open windows:', openWindows.length);

        // Clear grid
        grid.innerHTML = '';

        if (openWindows.length === 0) {
            grid.innerHTML = `
                <div style="grid-column: 1 / -1; text-align: center; color: rgba(255,255,255,0.6); padding: 40px;">
                    <h3>没有打开的窗口</h3>
                    <p>点击底部Dock启动应用</p>
                </div>
            `;
            return;
        }

        openWindows.forEach((window, index) => {
            console.log('Processing window:', window.getAttribute('data-app'));

            // Create overview card
            const overviewCard = document.createElement('div');
            overviewCard.className = 'overview-card';
            overviewCard.setAttribute('data-app', window.getAttribute('data-app'));

            // Get window title
            const windowTitle = window.querySelector('.window-title').textContent;

            // Create card content
            overviewCard.innerHTML = `
                <div class="overview-card-preview">
                    <div class="overview-card-header">
                        <div class="overview-card-title">${windowTitle}</div>
                        <div class="overview-card-status">${window.classList.contains('minimized') ? '已最小化' : '正在运行'}</div>
                    </div>
                    <div class="overview-card-content">
                        <div class="overview-card-icon">${getWindowIcon(window.getAttribute('data-app'))}</div>
                        <div class="overview-card-info">
                            <div class="overview-card-app">${getAppName(window.getAttribute('data-app'))}</div>
                            <div class="overview-card-desc">点击切换到此窗口</div>
                        </div>
                    </div>
                </div>
            `;

            // Add click handler to switch to window
            overviewCard.addEventListener('click', function(e) {
                e.stopPropagation();
                const appName = window.getAttribute('data-app');
                console.log('Switching to window:', appName);
                switchToWindow(appName);
                exitOverviewMode();
            });

            grid.appendChild(overviewCard);
        });
    }

    function getWindowIcon(appName) {
        const iconMap = {
            'home': '🏠',
            'products': '📦',
            'solutions': '🔧',
            'about': 'ℹ️',
            'contact': '📞'
        };
        return iconMap[appName] || '📄';
    }

    function getAppName(appName) {
        const nameMap = {
            'home': '首页',
            'products': '核心产品',
            'solutions': '解决方案',
            'about': '关于我们',
            'contact': '联系我们'
        };
        return nameMap[appName] || '应用程序';
    }

    function switchToWindow(appName) {
        const targetWindow = document.getElementById(appName + '-window');
        if (targetWindow) {
            // Restore if minimized
            if (targetWindow.classList.contains('minimized')) {
                targetWindow.classList.remove('minimized');
                targetWindow.classList.add('active');
            }

            // Bring to front
            bringToFront(targetWindow);

            // Taskbar removed
        }
    }

    function exitOverviewMode() {
        const desktop = document.querySelector('.ubuntu-desktop');
        const dock = document.querySelector('.mac-dock');
        // Taskbar removed
        const allWindows = document.querySelectorAll('.app-window');

        isOverviewMode = false;
        taskManagerButton.classList.remove('active');
        desktop.classList.remove('overview-mode');

        // Hide overview
        if (overviewContainer) {
            overviewContainer.classList.remove('active');
        }

        // Restore all windows display
        allWindows.forEach(window => {
            window.style.display = '';
        });

        // Restore dock
        if (dock) dock.style.opacity = '1';
    }

    // ESC key to exit overview
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && isOverviewMode) {
            exitOverviewMode();
        }
    });

    // Click outside to exit overview
    document.addEventListener('click', function(e) {
        if (isOverviewMode && overviewContainer &&
            !overviewContainer.contains(e.target) &&
            !taskManagerButton.contains(e.target)) {
            exitOverviewMode();
        }
    });
    
    // System indicators functionality
    const indicators = document.querySelectorAll('.indicator');
    
    indicators.forEach(indicator => {
        indicator.addEventListener('click', function() {
            // Simple notification effect
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 50px;
                right: 20px;
                background: rgba(44, 0, 30, 0.9);
                color: white;
                padding: 12px 16px;
                border-radius: 8px;
                font-size: 14px;
                z-index: 1000;
                animation: slideIn 0.3s ease;
            `;
            
            const messages = {
                '🔊': '音量: 75%',
                '📶': '网络: 已连接',
                '🔋': '电池: 85%',
                '⚙️': '设置面板'
            };
            
            notification.textContent = messages[this.textContent] || '系统通知';
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 2000);
        });
    });
    
    // Add slide-in animation
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
    `;
    document.head.appendChild(style);

    // Theme System
    const themeSystem = {
        currentTheme: 'ubuntu-default',

        themes: {
            'ubuntu-default': {
                name: 'Ubuntu 经典',
                background: 'linear-gradient(135deg, #e95420 0%, #772953 50%, #2c001e 100%)',
                topBar: 'rgba(44, 0, 30, 0.95)',
                topBarText: 'white',
                dock: 'linear-gradient(135deg, rgba(60, 60, 60, 0.95) 0%, rgba(40, 40, 40, 0.95) 50%, rgba(30, 30, 30, 0.95) 100%)',
                window: 'rgba(255, 255, 255, 0.95)',
                windowText: '#2c001e',
                windowHeader: 'linear-gradient(135deg, #3c3c3c, #2c2c2c)',
                windowHeaderText: 'white',
                accent: '#e95420',
                isDark: false
            },
            'ubuntu-dark': {
                name: 'Ubuntu 深色',
                background: 'linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #0f0f0f 100%)',
                topBar: 'rgba(20, 20, 20, 0.95)',
                topBarText: 'white',
                dock: 'linear-gradient(135deg, rgba(20, 20, 20, 0.95) 0%, rgba(15, 15, 15, 0.95) 50%, rgba(10, 10, 10, 0.95) 100%)',
                window: 'rgba(40, 40, 40, 0.95)',
                windowText: 'white',
                windowHeader: 'linear-gradient(135deg, #2a2a2a, #1a1a1a)',
                windowHeaderText: 'white',
                accent: '#bb86fc',
                isDark: true
            },
            'ubuntu-light': {
                name: 'Ubuntu 浅色',
                background: 'linear-gradient(135deg, #fafafa 0%, #f0f0f0 50%, #e8e8e8 100%)',
                topBar: 'rgba(248, 248, 248, 0.98)',
                topBarText: '#2c2c2c',
                dock: 'linear-gradient(135deg, rgba(248, 248, 248, 0.98) 0%, rgba(240, 240, 240, 0.98) 50%, rgba(235, 235, 235, 0.98) 100%)',
                window: 'rgba(255, 255, 255, 0.98)',
                windowText: '#2c2c2c',
                windowHeader: 'linear-gradient(135deg, #f8f8f8, #f0f0f0)',
                windowHeaderText: '#2c2c2c',
                accent: '#1976d2',
                isDark: false
            },
            'ubuntu-purple': {
                name: 'Ubuntu 紫色',
                background: 'linear-gradient(135deg, #6a1b9a 0%, #4a148c 50%, #2e0854 100%)',
                topBar: 'rgba(46, 8, 84, 0.95)',
                topBarText: 'white',
                dock: 'linear-gradient(135deg, rgba(106, 27, 154, 0.95) 0%, rgba(74, 20, 140, 0.95) 50%, rgba(46, 8, 84, 0.95) 100%)',
                window: 'rgba(255, 255, 255, 0.95)',
                windowText: '#2e0854',
                windowHeader: 'linear-gradient(135deg, #6a1b9a, #4a148c)',
                windowHeaderText: 'white',
                accent: '#9c27b0',
                isDark: false
            },
            'ubuntu-green': {
                name: 'Ubuntu 绿色',
                background: 'linear-gradient(135deg, #2e7d32 0%, #1b5e20 50%, #0d3f14 100%)',
                topBar: 'rgba(13, 63, 20, 0.95)',
                topBarText: 'white',
                dock: 'linear-gradient(135deg, rgba(46, 125, 50, 0.95) 0%, rgba(27, 94, 32, 0.95) 50%, rgba(13, 63, 20, 0.95) 100%)',
                window: 'rgba(255, 255, 255, 0.95)',
                windowText: '#0d3f14',
                windowHeader: 'linear-gradient(135deg, #2e7d32, #1b5e20)',
                windowHeaderText: 'white',
                accent: '#4caf50',
                isDark: false
            },
            'ubuntu-blue': {
                name: 'Ubuntu 蓝色',
                background: 'linear-gradient(135deg, #1976d2 0%, #0d47a1 50%, #0a2e5c 100%)',
                topBar: 'rgba(10, 46, 92, 0.95)',
                topBarText: 'white',
                dock: 'linear-gradient(135deg, rgba(25, 118, 210, 0.95) 0%, rgba(13, 71, 161, 0.95) 50%, rgba(10, 46, 92, 0.95) 100%)',
                window: 'rgba(255, 255, 255, 0.95)',
                windowText: '#0a2e5c',
                windowHeader: 'linear-gradient(135deg, #1976d2, #0d47a1)',
                windowHeaderText: 'white',
                accent: '#2196f3',
                isDark: false
            }
        },

        init() {
            this.loadSavedTheme();
            this.setupEventListeners();
        },

        loadSavedTheme() {
            const savedTheme = localStorage.getItem('ubuntu-theme');
            if (savedTheme && this.themes[savedTheme]) {
                this.currentTheme = savedTheme;
                this.applyTheme(savedTheme);
                this.updateActiveOption(savedTheme);
            }
        },

        setupEventListeners() {
            const themeSwitcher = document.getElementById('theme-switcher');
            const themePanel = document.getElementById('theme-panel');
            const themePanelClose = document.getElementById('theme-panel-close');
            const themeOptions = document.querySelectorAll('.theme-option');

            themeSwitcher.addEventListener('click', () => {
                themePanel.classList.toggle('active');
            });

            themePanelClose.addEventListener('click', () => {
                themePanel.classList.remove('active');
            });

            // Close panel when clicking outside
            themePanel.addEventListener('click', (e) => {
                if (e.target === themePanel) {
                    themePanel.classList.remove('active');
                }
            });

            themeOptions.forEach(option => {
                option.addEventListener('click', () => {
                    const themeName = option.getAttribute('data-theme');
                    this.switchTheme(themeName);
                    themePanel.classList.remove('active');
                });
            });
        },

        switchTheme(themeName) {
            if (!this.themes[themeName]) return;

            this.currentTheme = themeName;
            this.applyTheme(themeName);
            this.updateActiveOption(themeName);
            localStorage.setItem('ubuntu-theme', themeName);
        },

        applyTheme(themeName) {
            const theme = this.themes[themeName];
            const root = document.documentElement;

            // Apply CSS custom properties
            root.style.setProperty('--theme-background', theme.background);
            root.style.setProperty('--theme-top-bar', theme.topBar);
            root.style.setProperty('--theme-top-bar-text', theme.topBarText);
            root.style.setProperty('--theme-dock', theme.dock);
            root.style.setProperty('--theme-window', theme.window);
            root.style.setProperty('--theme-window-text', theme.windowText);
            root.style.setProperty('--theme-window-header', theme.windowHeader);
            root.style.setProperty('--theme-window-header-text', theme.windowHeaderText);
            root.style.setProperty('--theme-accent', theme.accent);

            // Apply to body background
            document.body.style.background = theme.background;

            // Apply to top bar
            const topBar = document.querySelector('.top-bar');
            if (topBar) {
                topBar.style.background = theme.topBar;
                topBar.style.color = theme.topBarText;
            }

            // Apply to dock
            const dockContainer = document.querySelector('.dock-container');
            if (dockContainer) {
                dockContainer.style.background = theme.dock;
            }

            // Apply to windows (including popups)
            const windows = document.querySelectorAll('.app-window, .popup-window');
            windows.forEach(window => {
                window.style.background = theme.window;
                window.style.color = theme.windowText;

                const header = window.querySelector('.ubuntu-header');
                if (header) {
                    header.style.background = theme.windowHeader;
                    header.style.color = theme.windowHeaderText;

                    // Update window title specifically
                    const windowTitle = header.querySelector('.window-title');
                    if (windowTitle) {
                        windowTitle.style.color = theme.windowHeaderText;
                    }
                }

                // Update window content text colors
                const windowContent = window.querySelector('.window-content');
                if (windowContent) {
                    windowContent.style.color = theme.windowText;
                }

                // Update card text colors
                const cards = window.querySelectorAll('.ubuntu-card');
                cards.forEach(card => {
                    card.style.color = theme.windowText;

                    // Update headings
                    const headings = card.querySelectorAll('h1, h2, h3, h4, h5, h6');
                    headings.forEach(heading => {
                        if (theme.isDark) {
                            heading.style.color = 'white';
                        } else {
                            heading.style.color = theme.windowText;
                        }
                    });

                    // Update paragraphs and text
                    const paragraphs = card.querySelectorAll('p, .contact-item, .solution-info p, .card-content p');
                    paragraphs.forEach(p => {
                        if (theme.isDark) {
                            p.style.color = 'rgba(255, 255, 255, 0.8)';
                        } else {
                            p.style.color = '#666';
                        }
                    });

                    // Update strong elements
                    const strongs = card.querySelectorAll('strong');
                    strongs.forEach(strong => {
                        strong.style.color = theme.windowText;
                    });
                });
            });

            // Update desktop background
            const desktopWallpaper = document.querySelector('.desktop-wallpaper');
            if (desktopWallpaper) {
                if (theme.isDark || themeName === 'ubuntu-default') {
                    desktopWallpaper.style.background = `linear-gradient(135deg,
                        rgba(233, 84, 32, 0.1) 0%,
                        rgba(119, 41, 83, 0.1) 50%,
                        rgba(44, 0, 30, 0.1) 100%)`;
                } else {
                    // 浅色主题使用纯色背景
                    desktopWallpaper.style.background = `linear-gradient(135deg,
                        rgba(250, 250, 250, 0.8) 0%,
                        rgba(245, 245, 245, 0.8) 50%,
                        rgba(240, 240, 240, 0.8) 100%)`;
                }
            }

            // Update welcome text colors
            const welcomeText = document.querySelector('.welcome-text');
            if (welcomeText) {
                const h1 = welcomeText.querySelector('h1');
                const p = welcomeText.querySelector('p');
                if (theme.isDark || themeName === 'ubuntu-default') {
                    if (h1) h1.style.color = 'rgba(255, 255, 255, 0.9)';
                    if (p) p.style.color = 'rgba(255, 255, 255, 0.7)';
                } else {
                    if (h1) h1.style.color = 'rgba(44, 44, 44, 0.9)';
                    if (p) p.style.color = 'rgba(44, 44, 44, 0.7)';
                }
            }

            // Update active dock item colors
            this.updateDockActiveColors(theme.accent);

            // Update dock label colors for light themes
            this.updateDockLabelColors(theme.isDark);

            // Update window control buttons for light themes
            this.updateWindowControlColors(theme.isDark);
        },

        updateDockActiveColors(accentColor) {
            const style = document.createElement('style');
            style.id = 'theme-dock-active-style';

            // Remove existing theme style
            const existingStyle = document.getElementById('theme-dock-active-style');
            if (existingStyle) {
                existingStyle.remove();
            }

            style.textContent = `
                .dock-item.active {
                    background: linear-gradient(135deg,
                        ${accentColor}30 0%,
                        ${accentColor}20 100%) !important;
                    border: 1px solid ${accentColor}99 !important;
                    box-shadow: 0 4px 12px ${accentColor}4d !important;
                }
                .dock-item.active::after {
                    background: ${accentColor} !important;
                    box-shadow: 0 2px 4px ${accentColor}80 !important;
                }
            `;

            document.head.appendChild(style);
        },

        updateDockLabelColors(isDark) {
            const style = document.createElement('style');
            style.id = 'theme-dock-label-style';

            // Remove existing label style
            const existingStyle = document.getElementById('theme-dock-label-style');
            if (existingStyle) {
                existingStyle.remove();
            }

            if (isDark) {
                style.textContent = `
                    .dock-label {
                        background: linear-gradient(135deg,
                            rgba(40, 40, 40, 0.95) 0%,
                            rgba(20, 20, 20, 0.95) 100%) !important;
                        color: white !important;
                        border: 1px solid rgba(255, 255, 255, 0.15) !important;
                    }
                `;
            } else {
                style.textContent = `
                    .dock-label {
                        background: linear-gradient(135deg,
                            rgba(248, 248, 248, 0.98) 0%,
                            rgba(240, 240, 240, 0.98) 100%) !important;
                        color: #2c2c2c !important;
                        border: 1px solid rgba(0, 0, 0, 0.12) !important;
                        box-shadow:
                            0 8px 24px rgba(0, 0, 0, 0.15),
                            inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;
                    }
                `;
            }

            document.head.appendChild(style);
        },

        updateWindowControlColors(isDark) {
            const style = document.createElement('style');
            style.id = 'theme-window-control-style';

            // Remove existing control style
            const existingStyle = document.getElementById('theme-window-control-style');
            if (existingStyle) {
                existingStyle.remove();
            }

            if (isDark) {
                // 深色主题保持原有的浅色按钮
                style.textContent = `
                    .ubuntu-control.minimize {
                        background: #ffbd44 !important;
                    }
                    .ubuntu-control.maximize {
                        background: #00ca56 !important;
                    }
                    .ubuntu-control.close {
                        background: #ff605c !important;
                    }
                    .ubuntu-control.minimize:hover {
                        background: #ffaa00 !important;
                    }
                    .ubuntu-control.maximize:hover {
                        background: #00b84a !important;
                    }
                    .ubuntu-control.close:hover {
                        background: #ff453a !important;
                    }
                `;
            } else {
                // 浅色主题使用更深的颜色以提高对比度
                style.textContent = `
                    .ubuntu-control.minimize {
                        background: #e6a000 !important;
                        border: 1px solid #cc8f00 !important;
                    }
                    .ubuntu-control.maximize {
                        background: #00a844 !important;
                        border: 1px solid #008f3a !important;
                    }
                    .ubuntu-control.close {
                        background: #e6453a !important;
                        border: 1px solid #cc3a2f !important;
                    }
                    .ubuntu-control.minimize:hover {
                        background: #cc8f00 !important;
                        border: 1px solid #b37d00 !important;
                    }
                    .ubuntu-control.maximize:hover {
                        background: #008f3a !important;
                        border: 1px solid #007530 !important;
                    }
                    .ubuntu-control.close:hover {
                        background: #cc3a2f !important;
                        border: 1px solid #b32f26 !important;
                    }
                `;
            }

            document.head.appendChild(style);
        },

        updateActiveOption(themeName) {
            const themeOptions = document.querySelectorAll('.theme-option');
            themeOptions.forEach(option => {
                option.classList.remove('active');
                if (option.getAttribute('data-theme') === themeName) {
                    option.classList.add('active');
                }
            });
        }
    };

    // Initialize theme system
    themeSystem.init();

    // Global window control functions for HTML onclick
    window.windowMinimize = function(appName) {
        minimizeWindow(appName);
    };

    window.windowMaximize = function(appName) {
        toggleMaximize(appName);
    };

    window.windowClose = function(appName) {
        closeWindow(appName);
    };

    // Status bar functionality
    const statusBarFeatures = {
        init() {
            this.setupMusicPlayer();
            this.setupBookmarks();
            this.setupWeChatQR();
            this.setupSettings();
        },

        setupMusicPlayer() {
            const musicIcon = document.getElementById('music-player');
            const musicModal = document.getElementById('music-modal');
            const musicClose = document.getElementById('music-modal-close');

            this.musicPlayer = {
                isPlaying: false,
                currentSong: 0,
                isShuffled: false,
                isRepeated: false,
                volume: 70,
                playlist: [
                    { title: 'Ubuntu 主题音乐', artist: 'Linux Community', duration: '3:42' },
                    { title: 'Open Source Symphony', artist: 'FOSS Orchestra', duration: '4:15' },
                    { title: 'Terminal Blues', artist: 'Command Line', duration: '3:28' },
                    { title: 'Code & Coffee', artist: 'Developer Vibes', duration: '5:03' },
                    { title: 'Digital Dreams', artist: 'Tech Ambient', duration: '4:37' }
                ]
            };

            musicIcon.addEventListener('click', () => {
                musicModal.classList.add('active');
                this.renderPlaylist();
            });

            musicClose.addEventListener('click', () => {
                musicModal.classList.remove('active');
            });

            musicModal.addEventListener('click', (e) => {
                if (e.target === musicModal) {
                    musicModal.classList.remove('active');
                }
            });

            this.setupMusicControls();
        },

        setupMusicControls() {
            const playBtn = document.getElementById('music-play');
            const prevBtn = document.getElementById('music-prev');
            const nextBtn = document.getElementById('music-next');
            const shuffleBtn = document.getElementById('music-shuffle');
            const repeatBtn = document.getElementById('music-repeat');
            const volumeSlider = document.getElementById('volume-slider');
            const addBtn = document.getElementById('playlist-add-btn');

            playBtn.addEventListener('click', () => {
                this.togglePlay();
            });

            prevBtn.addEventListener('click', () => {
                this.previousSong();
            });

            nextBtn.addEventListener('click', () => {
                this.nextSong();
            });

            shuffleBtn.addEventListener('click', () => {
                this.toggleShuffle();
            });

            repeatBtn.addEventListener('click', () => {
                this.toggleRepeat();
            });

            volumeSlider.addEventListener('input', (e) => {
                this.setVolume(e.target.value);
            });

            addBtn.addEventListener('click', () => {
                this.addNewSong();
            });
        },

        togglePlay() {
            this.musicPlayer.isPlaying = !this.musicPlayer.isPlaying;
            const playBtn = document.getElementById('music-play');
            playBtn.textContent = this.musicPlayer.isPlaying ? '⏸' : '▶';

            if (this.musicPlayer.isPlaying) {
                this.startMusicAnimation();
            } else {
                this.stopMusicAnimation();
            }
        },

        previousSong() {
            if (this.musicPlayer.currentSong > 0) {
                this.musicPlayer.currentSong--;
            } else {
                this.musicPlayer.currentSong = this.musicPlayer.playlist.length - 1;
            }
            this.updateCurrentSong();
        },

        nextSong() {
            if (this.musicPlayer.currentSong < this.musicPlayer.playlist.length - 1) {
                this.musicPlayer.currentSong++;
            } else {
                this.musicPlayer.currentSong = 0;
            }
            this.updateCurrentSong();
        },

        toggleShuffle() {
            this.musicPlayer.isShuffled = !this.musicPlayer.isShuffled;
            const shuffleBtn = document.getElementById('music-shuffle');
            shuffleBtn.style.background = this.musicPlayer.isShuffled ?
                'linear-gradient(135deg, #28a745, #20c997)' :
                'linear-gradient(135deg, #e95420, #ff6b35)';
        },

        toggleRepeat() {
            this.musicPlayer.isRepeated = !this.musicPlayer.isRepeated;
            const repeatBtn = document.getElementById('music-repeat');
            repeatBtn.style.background = this.musicPlayer.isRepeated ?
                'linear-gradient(135deg, #28a745, #20c997)' :
                'linear-gradient(135deg, #e95420, #ff6b35)';
        },

        setVolume(value) {
            this.musicPlayer.volume = value;
            document.getElementById('volume-value').textContent = value + '%';
        },

        updateCurrentSong() {
            const song = this.musicPlayer.playlist[this.musicPlayer.currentSong];
            document.getElementById('current-song-title').textContent = song.title;
            document.getElementById('current-song-artist').textContent = song.artist;
            document.getElementById('total-time').textContent = song.duration;
            this.renderPlaylist();
        },

        renderPlaylist() {
            const container = document.getElementById('playlist-items');
            container.innerHTML = '';

            this.musicPlayer.playlist.forEach((song, index) => {
                const item = document.createElement('div');
                item.className = `playlist-item ${index === this.musicPlayer.currentSong ? 'active' : ''}`;
                item.innerHTML = `
                    <div class="playlist-item-info">
                        <div class="playlist-item-title">${song.title}</div>
                        <div class="playlist-item-artist">${song.artist}</div>
                    </div>
                    <div class="playlist-item-duration">${song.duration}</div>
                    <button class="playlist-item-remove" onclick="statusBarFeatures.removeSong(${index})">×</button>
                `;

                item.addEventListener('click', (e) => {
                    if (!e.target.classList.contains('playlist-item-remove')) {
                        this.musicPlayer.currentSong = index;
                        this.updateCurrentSong();
                    }
                });

                container.appendChild(item);
            });
        },

        addNewSong() {
            const title = prompt('歌曲名称:');
            const artist = prompt('艺术家:');
            const duration = prompt('时长 (例: 3:42):');

            if (title && artist && duration) {
                this.musicPlayer.playlist.push({ title, artist, duration });
                this.renderPlaylist();
            }
        },

        removeSong(index) {
            if (this.musicPlayer.playlist.length > 1) {
                this.musicPlayer.playlist.splice(index, 1);
                if (this.musicPlayer.currentSong >= index && this.musicPlayer.currentSong > 0) {
                    this.musicPlayer.currentSong--;
                }
                this.updateCurrentSong();
            }
        },

        setupBookmarks() {
            const notesIcon = document.getElementById('notes');
            const notesModal = document.getElementById('notes-modal');
            const notesClose = document.getElementById('notes-modal-close');

            this.notesApp = {
                notes: JSON.parse(localStorage.getItem('ubuntu-notes') || '[]'),
                currentNote: null,
                filter: 'all'
            };

            notesIcon.addEventListener('click', () => {
                notesModal.classList.add('active');
                this.renderNotesList();
            });

            notesClose.addEventListener('click', () => {
                notesModal.classList.remove('active');
                this.hideNoteEditor();
            });

            notesModal.addEventListener('click', (e) => {
                if (e.target === notesModal) {
                    notesModal.classList.remove('active');
                    this.hideNoteEditor();
                }
            });

            this.setupNotesControls();
        },

        setupNotesControls() {
            const addBtn = document.getElementById('notes-add-btn');
            const filterSelect = document.getElementById('notes-filter');
            const saveBtn = document.getElementById('note-save-btn');
            const cancelBtn = document.getElementById('note-cancel-btn');
            const deleteBtn = document.getElementById('note-delete-btn');

            addBtn.addEventListener('click', () => {
                this.createNewNote();
            });

            filterSelect.addEventListener('change', (e) => {
                this.notesApp.filter = e.target.value;
                this.renderNotesList();
            });

            saveBtn.addEventListener('click', () => {
                this.saveCurrentNote();
            });

            cancelBtn.addEventListener('click', () => {
                this.hideNoteEditor();
            });

            deleteBtn.addEventListener('click', () => {
                this.deleteCurrentNote();
            });
        },

        createNewNote() {
            this.notesApp.currentNote = {
                id: Date.now(),
                title: '',
                content: '',
                category: 'work',
                createdAt: new Date(),
                updatedAt: new Date()
            };
            this.showNoteEditor();
        },

        editNote(noteId) {
            this.notesApp.currentNote = this.notesApp.notes.find(note => note.id === noteId);
            this.showNoteEditor();
        },

        showNoteEditor() {
            const editor = document.getElementById('note-editor');
            const titleInput = document.getElementById('note-title-input');
            const contentInput = document.getElementById('note-content-input');
            const categorySelect = document.getElementById('note-category');
            const deleteBtn = document.getElementById('note-delete-btn');

            editor.style.display = 'flex';

            if (this.notesApp.currentNote) {
                titleInput.value = this.notesApp.currentNote.title || '';
                contentInput.value = this.notesApp.currentNote.content || '';
                categorySelect.value = this.notesApp.currentNote.category || 'work';
                deleteBtn.style.display = this.notesApp.currentNote.id ? 'block' : 'none';
            }

            titleInput.focus();
        },

        hideNoteEditor() {
            const editor = document.getElementById('note-editor');
            editor.style.display = 'none';
            this.notesApp.currentNote = null;
        },

        saveCurrentNote() {
            const titleInput = document.getElementById('note-title-input');
            const contentInput = document.getElementById('note-content-input');
            const categorySelect = document.getElementById('note-category');

            if (!titleInput.value.trim()) {
                alert('请输入便签标题');
                return;
            }

            const note = this.notesApp.currentNote;
            note.title = titleInput.value.trim();
            note.content = contentInput.value.trim();
            note.category = categorySelect.value;
            note.updatedAt = new Date();

            const existingIndex = this.notesApp.notes.findIndex(n => n.id === note.id);
            if (existingIndex >= 0) {
                this.notesApp.notes[existingIndex] = note;
            } else {
                this.notesApp.notes.unshift(note);
            }

            this.saveNotesToStorage();
            this.renderNotesList();
            this.hideNoteEditor();
        },

        deleteCurrentNote() {
            if (confirm('确定要删除这个便签吗？')) {
                const noteId = this.notesApp.currentNote.id;
                this.notesApp.notes = this.notesApp.notes.filter(note => note.id !== noteId);
                this.saveNotesToStorage();
                this.renderNotesList();
                this.hideNoteEditor();
            }
        },

        renderNotesList() {
            const container = document.getElementById('notes-list');
            const filteredNotes = this.notesApp.filter === 'all' ?
                this.notesApp.notes :
                this.notesApp.notes.filter(note => note.category === this.notesApp.filter);

            container.innerHTML = '';

            if (filteredNotes.length === 0) {
                container.innerHTML = '<div style="text-align: center; color: #999; padding: 40px;">暂无便签</div>';
                return;
            }

            filteredNotes.forEach(note => {
                const item = document.createElement('div');
                item.className = 'note-item';
                item.innerHTML = `
                    <div class="note-item-header">
                        <div class="note-item-title">${note.title}</div>
                        <div class="note-item-category">${this.getCategoryName(note.category)}</div>
                    </div>
                    <div class="note-item-content">${note.content}</div>
                    <div class="note-item-date">${this.formatDate(note.updatedAt)}</div>
                `;

                item.addEventListener('click', () => {
                    this.editNote(note.id);
                });

                container.appendChild(item);
            });
        },

        getCategoryName(category) {
            const names = {
                'work': '工作',
                'personal': '个人',
                'ideas': '想法'
            };
            return names[category] || category;
        },

        formatDate(date) {
            const d = new Date(date);
            const now = new Date();
            const diff = now - d;

            if (diff < 60000) return '刚刚';
            if (diff < 3600000) return Math.floor(diff / 60000) + '分钟前';
            if (diff < 86400000) return Math.floor(diff / 3600000) + '小时前';
            if (diff < 604800000) return Math.floor(diff / 86400000) + '天前';

            return d.toLocaleDateString();
        },

        saveNotesToStorage() {
            localStorage.setItem('ubuntu-notes', JSON.stringify(this.notesApp.notes));
        },

        setupWeChatQR() {
            const wechatIcon = document.getElementById('wechat-qr');
            const wechatModal = document.getElementById('wechat-modal');
            const wechatClose = document.getElementById('wechat-modal-close');

            wechatIcon.addEventListener('click', () => {
                wechatModal.classList.add('active');
            });

            wechatClose.addEventListener('click', () => {
                wechatModal.classList.remove('active');
            });

            wechatModal.addEventListener('click', (e) => {
                if (e.target === wechatModal) {
                    wechatModal.classList.remove('active');
                }
            });
        },

        setupSettings() {
            const settingsIcon = document.getElementById('settings');

            settingsIcon.addEventListener('click', () => {
                const settings = [
                    { name: '主题设置', action: () => document.getElementById('theme-switcher').click() },
                    { name: '任务管理', action: () => document.querySelector('.task-manager-button').click() },
                    { name: '关于系统', action: () => openWindow('about') }
                ];

                this.showSettingsMenu(settingsIcon, settings);
            });
        },



        showSettingsMenu(icon, settings) {
            this.removeExistingMenu();

            const menu = document.createElement('div');
            menu.className = 'status-menu settings-menu';
            menu.innerHTML = `
                <div class="status-menu-header">系统设置</div>
                ${settings.map(setting => `
                    <div class="status-menu-item">
                        <span>${setting.name}</span>
                    </div>
                `).join('')}
            `;

            this.positionMenu(menu, icon);
            document.body.appendChild(menu);

            // Add click handlers
            settings.forEach((setting, index) => {
                const item = menu.children[index + 1];
                item.addEventListener('click', () => {
                    setting.action();
                    this.removeExistingMenu();
                });
            });

            setTimeout(() => menu.classList.add('active'), 10);
        },

        positionMenu(menu, icon) {
            const rect = icon.getBoundingClientRect();
            menu.style.position = 'fixed';
            menu.style.top = (rect.bottom + 8) + 'px';
            menu.style.right = (window.innerWidth - rect.right) + 'px';
            menu.style.zIndex = '10001';
        },

        removeExistingMenu() {
            const existingMenu = document.querySelector('.status-menu');
            if (existingMenu) {
                existingMenu.remove();
            }
        },

        startMusicAnimation() {
            const progressFill = document.querySelector('.progress-fill');
            const currentTime = document.querySelector('.current-time');

            let progress = 35;
            let seconds = 85; // 1:25

            this.musicInterval = setInterval(() => {
                progress += 0.5;
                seconds += 1;

                if (progress >= 100) {
                    progress = 0;
                    seconds = 0;
                }

                progressFill.style.width = progress + '%';

                const minutes = Math.floor(seconds / 60);
                const secs = seconds % 60;
                currentTime.textContent = `${minutes}:${secs.toString().padStart(2, '0')}`;
            }, 200);
        },

        stopMusicAnimation() {
            if (this.musicInterval) {
                clearInterval(this.musicInterval);
                this.musicInterval = null;
            }
        }
    };

    // Initialize status bar features
    statusBarFeatures.init();

    // Close status menus when clicking outside
    document.addEventListener('click', (e) => {
        if (!e.target.closest('.status-menu') && !e.target.closest('.indicator')) {
            statusBarFeatures.removeExistingMenu();
        }
    });
    
    // Clock update
    function updateClock() {
        const now = new Date();
        const options = { 
            month: 'long', 
            day: 'numeric',
            hour: '2-digit', 
            minute: '2-digit',
            hour12: false 
        };
        const timeString = now.toLocaleDateString('zh-CN', options);
        const clockElement = document.querySelector('.clock');
        if (clockElement) {
            clockElement.textContent = timeString.replace('年', '年 ');
        }
    }
    
    // Update clock every minute
    updateClock();
    setInterval(updateClock, 60000);
    
    // Smooth scrolling for content areas
    const contentAreas = document.querySelectorAll('.window-content');
    
    contentAreas.forEach(area => {
        area.style.scrollBehavior = 'smooth';
    });
    
    // Add loading animation on page load
    document.body.style.opacity = '0';
    document.body.style.transition = 'opacity 0.5s ease';
    
    setTimeout(() => {
        document.body.style.opacity = '1';
    }, 100);
    
    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Alt + number keys for navigation
        if (e.altKey) {
            const keyMap = {
                '1': 'home',
                '2': 'products', 
                '3': 'solutions',
                '4': 'about',
                '5': 'contact'
            };
            
            const targetView = keyMap[e.key];
            if (targetView) {
                e.preventDefault();
                const targetNav = document.querySelector(`[data-view="${targetView}"]`);
                if (targetNav) {
                    targetNav.click();
                }
            }
        }
    });
    
    // Auto-open home window on load
    setTimeout(() => {
        openWindow('home');
    }, 500);

    // Global window control functions
    window.windowClose = function(appName) {
        console.log('Closing window:', appName);
        closeWindow(appName);
    };

    window.windowMinimize = function(appName) {
        console.log('Minimizing window:', appName);
        minimizeWindow(appName);
    };

    window.windowMaximize = function(appName) {
        console.log('Toggling maximize for window:', appName);
        toggleMaximize(appName);
    };

    // Legacy test functions
    window.testClose = window.windowClose;
    window.testMinimize = window.windowMinimize;
    window.testMaximize = window.windowMaximize;

    console.log('Ubuntu style website loaded successfully! 🚀');
    console.log('Keyboard shortcuts: Alt + 1-5 for navigation');
    console.log('Window controls: Drag to move, click controls to minimize/maximize/close');
    console.log('Test functions: testClose("home"), testMinimize("home"), testMaximize("home")');
});
