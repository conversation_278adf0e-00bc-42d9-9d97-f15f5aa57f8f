// ==================== 幻灯片功能 ====================

// 幻灯片管理器
class SlideshowManager {
    constructor() {
        this.slides = document.querySelectorAll('.slide');
        this.currentSlideIndex = 0;
        this.totalSlides = this.slides.length;
        this.autoPlayInterval = null;
        this.autoPlayDelay = 6000; // 6秒自动切换

        this.init();
    }

    init() {
        this.startAutoPlay();

        // 键盘导航
        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowLeft') this.prevSlide();
            if (e.key === 'ArrowRight') this.nextSlide();
            if (e.key >= '1' && e.key <= '4') {
                this.goToSlide(parseInt(e.key) - 1);
            }
        });

        // 鼠标悬停暂停自动播放
        const container = document.querySelector('.desktop-container');
        if (container) {
            container.addEventListener('mouseenter', () => this.stopAutoPlay());
            container.addEventListener('mouseleave', () => this.startAutoPlay());
        }
    }

    goToSlide(index) {
        if (index < 0 || index >= this.totalSlides) return;

        // 移除所有活跃状态
        this.slides.forEach(slide => {
            slide.classList.remove('active', 'prev');
        });

        // 设置前一张幻灯片
        if (this.currentSlideIndex < index) {
            this.slides[this.currentSlideIndex].classList.add('prev');
        }

        // 设置当前幻灯片
        this.currentSlideIndex = index;
        this.slides[this.currentSlideIndex].classList.add('active');

        this.updateNavActiveState();
    }

    nextSlide() {
        const nextIndex = (this.currentSlideIndex + 1) % this.totalSlides;
        this.goToSlide(nextIndex);
    }

    prevSlide() {
        const prevIndex = (this.currentSlideIndex - 1 + this.totalSlides) % this.totalSlides;
        this.goToSlide(prevIndex);
    }

    updateNavActiveState() {
        // 更新导航菜单的活跃状态
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach((link, index) => {
            if (index < 4) { // 只处理前4个导航项（对应4个幻灯片）
                link.classList.toggle('active', index === this.currentSlideIndex);
            }
        });
    }

    startAutoPlay() {
        this.stopAutoPlay();
        this.autoPlayInterval = setInterval(() => {
            this.nextSlide();
        }, this.autoPlayDelay);
    }

    stopAutoPlay() {
        if (this.autoPlayInterval) {
            clearInterval(this.autoPlayInterval);
            this.autoPlayInterval = null;
        }
    }
}

// 全局函数供HTML调用
let slideshowManager;

function nextSlide() {
    if (slideshowManager) slideshowManager.nextSlide();
}

function prevSlide() {
    if (slideshowManager) slideshowManager.prevSlide();
}

function currentSlide(index) {
    if (slideshowManager) slideshowManager.goToSlide(index - 1);
}

// 简洁的视差效果
class SimpleParallax {
    constructor() {
        this.bindAnimations();
    }

    bindAnimations() {
        const parallaxElements = document.querySelectorAll('.floating-elements .element');

        // 简单的浮动动画
        parallaxElements.forEach((element, index) => {
            element.style.animationDelay = `${index * 0.5}s`;
        });
    }
}

// ==================== 原有功能保留 ====================

// 简洁导航增强
class SimpleNavigation {
    constructor() {
        this.navLinks = document.querySelectorAll('.nav-link');
        this.init();
    }

    init() {
        this.setupSmoothScrolling();
    }

    setupSmoothScrolling() {
        this.navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = link.getAttribute('href');
                const target = document.querySelector(targetId);

                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }
}

// ==================== 初始化 ====================

// 页面加载完成后初始化
window.addEventListener('load', () => {
    // 基础加载动画
    document.body.style.opacity = '1';

    // 初始化导航
    new SimpleNavigation();

    // 初始化幻灯片
    slideshowManager = new SlideshowManager();

    // 初始化简洁视差效果
    new SimpleParallax();
});

// 初始化页面透明度
document.body.style.opacity = '0';
document.body.style.transition = 'opacity 0.5s ease';

// ==================== 桌面功能 ====================

// 为了向后兼容，保留这些全局函数
function nextSlide() {
    if (window.slideshowManager) {
        window.slideshowManager.nextSlide();
    }
}

function previousSlide() {
    if (window.slideshowManager) {
        window.slideshowManager.prevSlide();
    }
}

function currentSlide(index) {
    if (window.slideshowManager) {
        window.slideshowManager.goToSlide(index - 1);
    }
}

// 终端功能
function openTerminal() {
    const terminal = document.getElementById('terminal');
    if (terminal) {
        terminal.classList.add('active');
        // 聚焦到终端
        setTimeout(() => {
            const terminalContent = document.getElementById('terminal-content');
            if (terminalContent) {
                terminalContent.scrollTop = terminalContent.scrollHeight;
            }
        }, 100);
    }
}

function closeTerminal() {
    const terminal = document.getElementById('terminal');
    if (terminal) {
        terminal.classList.remove('active');
    }
}

// 桌面初始化
document.addEventListener('DOMContentLoaded', () => {
    // 添加ESC键关闭终端
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            closeTerminal();
        }
    });

    // 初始化终端功能
    initTerminal();
});

// 终端命令处理
function initTerminal() {
    const terminalContent = document.getElementById('terminal-content');
    let commandHistory = [];
    let historyIndex = -1;

    // 模拟终端输入
    document.addEventListener('keydown', (e) => {
        const terminal = document.getElementById('terminal');
        if (!terminal || !terminal.classList.contains('active')) return;

        if (e.key === 'Enter') {
            handleTerminalCommand();
        } else if (e.key === 'ArrowUp') {
            e.preventDefault();
            navigateHistory(-1);
        } else if (e.key === 'ArrowDown') {
            e.preventDefault();
            navigateHistory(1);
        } else if (e.key.length === 1 || e.key === 'Backspace') {
            handleTerminalInput(e);
        }
    });

    function handleTerminalInput(e) {
        const cursor = document.getElementById('terminal-cursor');
        if (!cursor) return;

        const currentLine = cursor.parentElement;
        let currentCommand = currentLine.textContent.split('$ ')[1] || '';

        if (e.key === 'Backspace') {
            currentCommand = currentCommand.slice(0, -1);
        } else {
            currentCommand += e.key;
        }

        currentLine.innerHTML = `<span class="terminal-prompt">qingyi@tech:~$</span> ${currentCommand}<span id="terminal-cursor">_</span>`;
    }

    function handleTerminalCommand() {
        const cursor = document.getElementById('terminal-cursor');
        if (!cursor) return;

        const currentLine = cursor.parentElement;
        const command = currentLine.textContent.split('$ ')[1]?.replace('_', '').trim() || '';

        if (command) {
            commandHistory.push(command);
            historyIndex = commandHistory.length;
        }

        // 移除光标
        cursor.remove();

        // 执行命令
        executeCommand(command);

        // 添加新的输入行
        addNewPrompt();
    }

    function executeCommand(command) {
        const output = getCommandOutput(command);
        addTerminalOutput(output);
    }

    function getCommandOutput(command) {
        switch (command.toLowerCase()) {
            case 'help':
                return `可用命令:
- help: 显示帮助信息
- products: 查看产品列表
- solutions: 查看解决方案
- about: 关于我们
- contact: 联系方式
- clear: 清屏
- exit: 关闭终端`;

            case 'products':
                return `核心产品:
🚀 智能云平台 - AI驱动的企业级云计算解决方案
🔮 数据分析引擎 - 实时大数据处理与智能分析平台
⚡ 智能自动化平台 - 端到端业务流程自动化解决方案`;

            case 'solutions':
                return `解决方案:
💼 企业数字化转型
🏭 智能制造解决方案
🏢 智慧办公平台
📊 数据驱动决策系统`;

            case 'about':
                return `北京卿逸科技中心
致力于将传统智慧与现代科技完美融合
为企业数字化转型提供前沿解决方案`;

            case 'contact':
                return `联系方式:
📧 Email: <EMAIL>
📞 电话: 400-123-4567
🏢 地址: 北京市朝阳区科技园区`;

            case 'clear':
                clearTerminal();
                return '';

            case 'exit':
                closeTerminal();
                return '';

            case '':
                return '';

            default:
                return `命令未找到: ${command}
输入 'help' 查看可用命令`;
        }
    }

    function addTerminalOutput(output) {
        if (!output) return;

        const terminalContent = document.getElementById('terminal-content');
        const outputDiv = document.createElement('div');
        outputDiv.className = 'terminal-output';
        outputDiv.textContent = output;
        terminalContent.appendChild(outputDiv);

        // 滚动到底部
        terminalContent.scrollTop = terminalContent.scrollHeight;
    }

    function addNewPrompt() {
        const terminalContent = document.getElementById('terminal-content');
        const promptDiv = document.createElement('div');
        promptDiv.className = 'terminal-output';
        promptDiv.innerHTML = `<span class="terminal-prompt">qingyi@tech:~$</span> <span id="terminal-cursor">_</span>`;
        terminalContent.appendChild(promptDiv);

        // 滚动到底部
        terminalContent.scrollTop = terminalContent.scrollHeight;
    }

    function clearTerminal() {
        const terminalContent = document.getElementById('terminal-content');
        terminalContent.innerHTML = `
            <div class="terminal-output">
                <span class="terminal-prompt">qingyi@tech:~$</span> clear
            </div>
            <div class="terminal-output">
                <span class="terminal-prompt">qingyi@tech:~$</span> <span id="terminal-cursor">_</span>
            </div>
        `;
    }

    function navigateHistory(direction) {
        if (commandHistory.length === 0) return;

        historyIndex += direction;
        if (historyIndex < 0) historyIndex = 0;
        if (historyIndex >= commandHistory.length) historyIndex = commandHistory.length;

        const cursor = document.getElementById('terminal-cursor');
        if (!cursor) return;

        const currentLine = cursor.parentElement;
        const command = historyIndex < commandHistory.length ? commandHistory[historyIndex] : '';

        currentLine.innerHTML = `<span class="terminal-prompt">qingyi@tech:~$</span> ${command}<span id="terminal-cursor">_</span>`;
    }
}