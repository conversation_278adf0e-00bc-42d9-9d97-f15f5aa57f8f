// ==================== 幻灯片功能 ====================

// 幻灯片管理器
class SlideshowManager {
    constructor() {
        this.slides = document.querySelectorAll('.slide');
        this.currentSlideIndex = 0;
        this.totalSlides = this.slides.length;
        this.autoPlayInterval = null;
        this.autoPlayDelay = 6000; // 6秒自动切换

        this.init();
    }

    init() {
        this.startAutoPlay();

        // 键盘导航
        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowLeft') this.prevSlide();
            if (e.key === 'ArrowRight') this.nextSlide();
            if (e.key >= '1' && e.key <= '4') {
                this.goToSlide(parseInt(e.key) - 1);
            }
        });

        // 鼠标悬停暂停自动播放
        const container = document.querySelector('.slideshow-container');
        container.addEventListener('mouseenter', () => this.stopAutoPlay());
        container.addEventListener('mouseleave', () => this.startAutoPlay());
    }

    goToSlide(index) {
        if (index < 0 || index >= this.totalSlides) return;

        // 移除所有活跃状态
        this.slides.forEach(slide => {
            slide.classList.remove('active', 'prev');
        });

        // 设置前一张幻灯片
        if (this.currentSlideIndex < index) {
            this.slides[this.currentSlideIndex].classList.add('prev');
        }

        // 设置当前幻灯片
        this.currentSlideIndex = index;
        this.slides[this.currentSlideIndex].classList.add('active');

        this.updateNavActiveState();
    }

    nextSlide() {
        const nextIndex = (this.currentSlideIndex + 1) % this.totalSlides;
        this.goToSlide(nextIndex);
    }

    prevSlide() {
        const prevIndex = (this.currentSlideIndex - 1 + this.totalSlides) % this.totalSlides;
        this.goToSlide(prevIndex);
    }

    updateNavActiveState() {
        // 更新导航菜单的活跃状态
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach((link, index) => {
            if (index < 4) { // 只处理前4个导航项（对应4个幻灯片）
                link.classList.toggle('active', index === this.currentSlideIndex);
            }
        });
    }

    startAutoPlay() {
        this.stopAutoPlay();
        this.autoPlayInterval = setInterval(() => {
            this.nextSlide();
        }, this.autoPlayDelay);
    }

    stopAutoPlay() {
        if (this.autoPlayInterval) {
            clearInterval(this.autoPlayInterval);
            this.autoPlayInterval = null;
        }
    }
}

// 全局函数供HTML调用
let slideshowManager;

function nextSlide() {
    if (slideshowManager) slideshowManager.nextSlide();
}

function prevSlide() {
    if (slideshowManager) slideshowManager.prevSlide();
}

function currentSlide(index) {
    if (slideshowManager) slideshowManager.goToSlide(index - 1);
}

// 简洁的视差效果
class SimpleParallax {
    constructor() {
        this.bindAnimations();
    }

    bindAnimations() {
        const parallaxElements = document.querySelectorAll('.floating-elements .element');

        // 简单的浮动动画
        parallaxElements.forEach((element, index) => {
            element.style.animationDelay = `${index * 0.5}s`;
        });
    }
}

// ==================== 原有功能保留 ====================

// 简洁导航增强
class SimpleNavigation {
    constructor() {
        this.navLinks = document.querySelectorAll('.nav-link');
        this.init();
    }

    init() {
        this.setupSmoothScrolling();
    }

    setupSmoothScrolling() {
        this.navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = link.getAttribute('href');
                const target = document.querySelector(targetId);

                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }
}

// ==================== 初始化 ====================

// 页面加载完成后初始化
window.addEventListener('load', () => {
    // 基础加载动画
    document.body.style.opacity = '1';

    // 初始化导航
    new SimpleNavigation();

    // 初始化幻灯片
    slideshowManager = new SlideshowManager();

    // 初始化简洁视差效果
    new SimpleParallax();
});

// 初始化页面透明度
document.body.style.opacity = '0';
document.body.style.transition = 'opacity 0.5s ease';