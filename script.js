// ==================== 简洁科技效果 ====================

// 标签页功能
class TabManager {
    constructor() {
        this.tabBtns = document.querySelectorAll('.tab-btn');
        this.tabPanels = document.querySelectorAll('.tab-panel');
        this.init();
    }

    init() {
        this.tabBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const targetTab = e.target.getAttribute('data-tab');
                this.switchTab(targetTab);
            });
        });
    }

    switchTab(targetTab) {
        // 移除所有活跃状态
        this.tabBtns.forEach(btn => btn.classList.remove('active'));
        this.tabPanels.forEach(panel => panel.classList.remove('active'));

        // 添加活跃状态
        const activeBtn = document.querySelector(`[data-tab="${targetTab}"]`);
        const activePanel = document.getElementById(targetTab);

        if (activeBtn && activePanel) {
            activeBtn.classList.add('active');
            activePanel.classList.add('active');
        }
    }
}

// 简洁的视差效果
class SimpleParallax {
    constructor() {
        this.bindScrollEvents();
    }

    bindScrollEvents() {
        const parallaxElements = document.querySelectorAll('.floating-elements .element');

        // 简单的浮动动画，不依赖滚动
        parallaxElements.forEach((element, index) => {
            element.style.animationDelay = `${index * 0.5}s`;
        });
    }
}

// ==================== 原有功能保留 ====================

// 简洁导航增强
class SimpleNavigation {
    constructor() {
        this.navLinks = document.querySelectorAll('.nav-link');
        this.init();
    }

    init() {
        this.setupSmoothScrolling();
    }

    setupSmoothScrolling() {
        this.navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = link.getAttribute('href');
                const target = document.querySelector(targetId);

                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }
}

// ==================== 初始化 ====================

// 页面加载完成后初始化
window.addEventListener('load', () => {
    // 基础加载动画
    document.body.style.opacity = '1';

    // 初始化导航
    new SimpleNavigation();

    // 初始化标签页
    new TabManager();

    // 初始化简洁视差效果
    new SimpleParallax();
});

// 初始化页面透明度
document.body.style.opacity = '0';
document.body.style.transition = 'opacity 0.5s ease';