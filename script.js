// Ubuntu Style JavaScript
console.log('Ubuntu script loading...');

document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing Ubuntu interface...');

    // Window management system
    let activeWindows = new Set();
    let windowZIndex = 100;
    let draggedWindow = null;
    let dragOffset = { x: 0, y: 0 };

    // Navigation functionality for both Ubuntu menu and Mac dock
    const navItems = document.querySelectorAll('[data-view]');

    // Handle navigation clicks - open windows
    navItems.forEach(item => {
        item.addEventListener('click', function() {
            const targetApp = this.getAttribute('data-view');
            openWindow(targetApp);
        });
    });

    // Handle action buttons that open other windows
    document.addEventListener('click', function(e) {
        if (e.target.hasAttribute('data-open')) {
            const targetApp = e.target.getAttribute('data-open');
            openWindow(targetApp);
        }
    });

    // Open window function
    function openWindow(appName) {
        console.log('Opening window for app:', appName);
        const window = document.getElementById(appName + '-window');
        if (!window) {
            console.error('Window not found:', appName + '-window');
            return;
        }

        console.log('Window found:', window);

        // If window is already open, just bring it to front
        if (activeWindows.has(appName)) {
            console.log('Window already open, bringing to front');
            bringToFront(window);
            return;
        }

        // Show window
        console.log('Showing window');
        window.classList.add('active', 'opening');
        window.style.zIndex = ++windowZIndex;
        activeWindows.add(appName);

        // Setup controls for this window
        const windowControls = window.querySelectorAll('.ubuntu-control');
        windowControls.forEach(control => {
            control.removeEventListener('click', handleControlClick);
            control.addEventListener('click', handleControlClick);
        });

        // Taskbar removed

        // Apply current theme to new window
        if (typeof themeSystem !== 'undefined' && themeSystem.currentTheme) {
            const theme = themeSystem.themes[themeSystem.currentTheme];
            if (theme) {
                window.style.background = theme.window;
                window.style.color = theme.windowText;

                const header = window.querySelector('.ubuntu-header');
                if (header) {
                    header.style.background = theme.windowHeader;
                    header.style.color = theme.windowHeaderText;

                    const windowTitle = header.querySelector('.window-title');
                    if (windowTitle) {
                        windowTitle.style.color = theme.windowHeaderText;
                    }
                }

                // Apply theme to window content
                const windowContent = window.querySelector('.window-content');
                if (windowContent) {
                    windowContent.style.color = theme.windowText;
                }
            }
        }

        // Remove opening animation class
        setTimeout(() => {
            window.classList.remove('opening');
        }, 300);

        // Update dock active state
        updateDockActiveState();
    }

    // Close window function
    function closeWindow(appName) {
        const window = document.getElementById(appName + '-window');
        if (!window) return;

        window.classList.add('closing');

        setTimeout(() => {
            window.classList.remove('active', 'closing');
            activeWindows.delete(appName);
            // Taskbar removed
            updateDockActiveState();
        }, 300);
    }

    // Minimize window function
    function minimizeWindow(appName) {
        const window = document.getElementById(appName + '-window');
        if (!window) return;

        window.classList.add('minimized');
        window.classList.remove('active');

        // Taskbar removed
    }

    // Maximize/restore window function
    function toggleMaximize(appName) {
        const window = document.getElementById(appName + '-window');
        if (!window) return;

        window.classList.toggle('maximized');
    }

    // Bring window to front
    function bringToFront(window) {
        window.style.zIndex = ++windowZIndex;

        // Taskbar removed
    }
    
    // Window controls functionality - Direct event binding
    function setupWindowControls() {
        console.log('Setting up window controls...');

        // Get all control buttons
        const allControls = document.querySelectorAll('.ubuntu-control');
        console.log('Found controls:', allControls.length);

        allControls.forEach((control, index) => {
            console.log(`Setting up control ${index}:`, control.getAttribute('data-action'));

            // Remove any existing listeners
            control.removeEventListener('click', handleControlClick);

            // Add click listener
            control.addEventListener('click', handleControlClick);
        });
    }

    function handleControlClick(e) {
        e.preventDefault();
        e.stopPropagation();

        const control = e.currentTarget;
        const window = control.closest('.app-window');

        if (!window) {
            console.error('Window not found for control');
            return;
        }

        const appName = window.getAttribute('data-app');
        const action = control.getAttribute('data-action');

        console.log('Control clicked:', action, 'for app:', appName);

        // Visual feedback
        control.style.transform = 'scale(0.9)';
        setTimeout(() => {
            control.style.transform = 'scale(1)';
        }, 150);

        // Execute action
        switch(action) {
            case 'close':
                console.log('Closing window:', appName);
                closeWindow(appName);
                break;
            case 'minimize':
                console.log('Minimizing window:', appName);
                minimizeWindow(appName);
                break;
            case 'maximize':
                console.log('Toggling maximize for window:', appName);
                toggleMaximize(appName);
                break;
            default:
                console.error('Unknown action:', action);
        }
    }

    // Setup controls after DOM is ready
    setupWindowControls();

    // Taskbar removed - no longer needed

    function updateDockActiveState() {
        // Update dock items to show which apps are open
        document.querySelectorAll('.dock-item').forEach(item => {
            const appName = item.getAttribute('data-view');
            if (activeWindows.has(appName)) {
                item.classList.add('active');
            } else {
                item.classList.remove('active');
            }
        });
    }
    
    // Ubuntu button hover effects
    const ubuntuButtons = document.querySelectorAll('.ubuntu-btn');
    
    ubuntuButtons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });
        
        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
        
        button.addEventListener('click', function() {
            // Click animation
            this.style.transform = 'translateY(0) scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'translateY(-2px) scale(1)';
            }, 150);
        });
    });
    
    // Card hover effects
    const cards = document.querySelectorAll('.ubuntu-card');
    
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.boxShadow = '0 8px 24px rgba(0, 0, 0, 0.15)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)';
        });
    });
    
    // Window dragging functionality
    document.addEventListener('mousedown', function(e) {
        const windowHeader = e.target.closest('.window-header');
        if (windowHeader && !e.target.classList.contains('control')) {
            const window = windowHeader.closest('.app-window');
            if (window.classList.contains('maximized')) return; // Can't drag maximized windows

            draggedWindow = window;
            const rect = window.getBoundingClientRect();
            dragOffset.x = e.clientX - rect.left;
            dragOffset.y = e.clientY - rect.top;

            window.classList.add('dragging');
            bringToFront(window);

            e.preventDefault();
        }
    });

    document.addEventListener('mousemove', function(e) {
        if (draggedWindow) {
            const x = e.clientX - dragOffset.x;
            const y = e.clientY - dragOffset.y;

            // Constrain to viewport
            const maxX = window.innerWidth - draggedWindow.offsetWidth;
            const maxY = window.innerHeight - draggedWindow.offsetHeight;

            draggedWindow.style.left = Math.max(0, Math.min(x, maxX)) + 'px';
            draggedWindow.style.top = Math.max(32, Math.min(y, maxY)) + 'px'; // 32px for top bar
        }
    });

    document.addEventListener('mouseup', function() {
        if (draggedWindow) {
            draggedWindow.classList.remove('dragging');
            draggedWindow = null;
        }
    });

    // Window click to bring to front
    document.addEventListener('mousedown', function(e) {
        const window = e.target.closest('.app-window');
        if (window) {
            bringToFront(window);
        }
    });

    // Ubuntu-style dock - no magnification effects
    // Labels are controlled by CSS hover states

    // Task Manager functionality - Ubuntu Task Overview
    const taskManagerButton = document.querySelector('.task-manager-button');
    let isOverviewMode = false;
    let overviewContainer = null;

    // Create overview container
    function createOverviewContainer() {
        if (overviewContainer) return;

        overviewContainer = document.createElement('div');
        overviewContainer.className = 'overview-container';
        overviewContainer.innerHTML = `
            <div class="overview-header">
                <h2>任务管理器</h2>
                <p>点击窗口切换到该应用，按ESC键退出</p>
            </div>
            <div class="overview-grid" id="overview-grid"></div>
        `;
        document.body.appendChild(overviewContainer);
    }

    taskManagerButton.addEventListener('click', function() {
        toggleTaskOverview();
    });

    function toggleTaskOverview() {
        const desktop = document.querySelector('.ubuntu-desktop');
        const dock = document.querySelector('.mac-dock');
        // Taskbar removed
        const allWindows = document.querySelectorAll('.app-window');

        isOverviewMode = !isOverviewMode;

        if (isOverviewMode) {
            // Enter overview mode
            taskManagerButton.classList.add('active');
            desktop.classList.add('overview-mode');

            // Hide all normal windows
            allWindows.forEach(window => {
                window.style.display = 'none';
            });

            // Create overview container if not exists
            createOverviewContainer();

            // Show overview
            overviewContainer.classList.add('active');

            // Populate overview grid
            populateOverviewGrid();

            // Hide dock in overview
            if (dock) dock.style.opacity = '0.2';

        } else {
            exitOverviewMode();
        }
    }

    function populateOverviewGrid() {
        const grid = document.getElementById('overview-grid');

        // Get all windows that are in activeWindows set (opened windows)
        const openWindows = [];
        activeWindows.forEach(appName => {
            const window = document.getElementById(appName + '-window');
            if (window) {
                openWindows.push(window);
            }
        });

        console.log('Active windows:', activeWindows);
        console.log('Found open windows:', openWindows.length);

        // Clear grid
        grid.innerHTML = '';

        if (openWindows.length === 0) {
            grid.innerHTML = `
                <div style="grid-column: 1 / -1; text-align: center; color: rgba(255,255,255,0.6); padding: 40px;">
                    <h3>没有打开的窗口</h3>
                    <p>点击底部Dock启动应用</p>
                </div>
            `;
            return;
        }

        openWindows.forEach((window, index) => {
            console.log('Processing window:', window.getAttribute('data-app'));

            // Create overview card
            const overviewCard = document.createElement('div');
            overviewCard.className = 'overview-card';
            overviewCard.setAttribute('data-app', window.getAttribute('data-app'));

            // Get window title
            const windowTitle = window.querySelector('.window-title').textContent;

            // Create card content
            overviewCard.innerHTML = `
                <div class="overview-card-preview">
                    <div class="overview-card-header">
                        <div class="overview-card-title">${windowTitle}</div>
                        <div class="overview-card-status">${window.classList.contains('minimized') ? '已最小化' : '正在运行'}</div>
                    </div>
                    <div class="overview-card-content">
                        <div class="overview-card-icon">${getWindowIcon(window.getAttribute('data-app'))}</div>
                        <div class="overview-card-info">
                            <div class="overview-card-app">${getAppName(window.getAttribute('data-app'))}</div>
                            <div class="overview-card-desc">点击切换到此窗口</div>
                        </div>
                    </div>
                </div>
            `;

            // Add click handler to switch to window
            overviewCard.addEventListener('click', function(e) {
                e.stopPropagation();
                const appName = window.getAttribute('data-app');
                console.log('Switching to window:', appName);
                switchToWindow(appName);
                exitOverviewMode();
            });

            grid.appendChild(overviewCard);
        });
    }

    function getWindowIcon(appName) {
        const iconMap = {
            'home': '🏠',
            'products': '📦',
            'solutions': '🔧',
            'about': 'ℹ️',
            'contact': '📞'
        };
        return iconMap[appName] || '📄';
    }

    function getAppName(appName) {
        const nameMap = {
            'home': '首页',
            'products': '核心产品',
            'solutions': '解决方案',
            'about': '关于我们',
            'contact': '联系我们'
        };
        return nameMap[appName] || '应用程序';
    }

    function switchToWindow(appName) {
        const targetWindow = document.getElementById(appName + '-window');
        if (targetWindow) {
            // Restore if minimized
            if (targetWindow.classList.contains('minimized')) {
                targetWindow.classList.remove('minimized');
                targetWindow.classList.add('active');
            }

            // Bring to front
            bringToFront(targetWindow);

            // Taskbar removed
        }
    }

    function exitOverviewMode() {
        const desktop = document.querySelector('.ubuntu-desktop');
        const dock = document.querySelector('.mac-dock');
        // Taskbar removed
        const allWindows = document.querySelectorAll('.app-window');

        isOverviewMode = false;
        taskManagerButton.classList.remove('active');
        desktop.classList.remove('overview-mode');

        // Hide overview
        if (overviewContainer) {
            overviewContainer.classList.remove('active');
        }

        // Restore all windows display
        allWindows.forEach(window => {
            window.style.display = '';
        });

        // Restore dock
        if (dock) dock.style.opacity = '1';
    }

    // ESC key to exit overview
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && isOverviewMode) {
            exitOverviewMode();
        }
    });

    // Click outside to exit overview
    document.addEventListener('click', function(e) {
        if (isOverviewMode && overviewContainer &&
            !overviewContainer.contains(e.target) &&
            !taskManagerButton.contains(e.target)) {
            exitOverviewMode();
        }
    });
    
    // System indicators functionality
    const indicators = document.querySelectorAll('.indicator');
    
    indicators.forEach(indicator => {
        indicator.addEventListener('click', function() {
            // Simple notification effect
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 50px;
                right: 20px;
                background: rgba(44, 0, 30, 0.9);
                color: white;
                padding: 12px 16px;
                border-radius: 8px;
                font-size: 14px;
                z-index: 1000;
                animation: slideIn 0.3s ease;
            `;
            
            const messages = {
                '🔊': '音量: 75%',
                '📶': '网络: 已连接',
                '🔋': '电池: 85%',
                '⚙️': '设置面板'
            };
            
            notification.textContent = messages[this.textContent] || '系统通知';
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 2000);
        });
    });
    
    // Add slide-in animation
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
    `;
    document.head.appendChild(style);

    // Theme System
    const themeSystem = {
        currentTheme: 'ubuntu-default',

        themes: {
            'ubuntu-default': {
                name: 'Ubuntu 经典',
                background: 'linear-gradient(135deg, #e95420 0%, #772953 50%, #2c001e 100%)',
                topBar: 'rgba(44, 0, 30, 0.95)',
                topBarText: 'white',
                dock: 'linear-gradient(135deg, rgba(60, 60, 60, 0.95) 0%, rgba(40, 40, 40, 0.95) 50%, rgba(30, 30, 30, 0.95) 100%)',
                window: 'rgba(255, 255, 255, 0.95)',
                windowText: '#2c001e',
                windowHeader: 'linear-gradient(135deg, #3c3c3c, #2c2c2c)',
                windowHeaderText: 'white',
                accent: '#e95420',
                isDark: false
            },
            'ubuntu-dark': {
                name: 'Ubuntu 深色',
                background: 'linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #0f0f0f 100%)',
                topBar: 'rgba(20, 20, 20, 0.95)',
                topBarText: 'white',
                dock: 'linear-gradient(135deg, rgba(20, 20, 20, 0.95) 0%, rgba(15, 15, 15, 0.95) 50%, rgba(10, 10, 10, 0.95) 100%)',
                window: 'rgba(40, 40, 40, 0.95)',
                windowText: 'white',
                windowHeader: 'linear-gradient(135deg, #2a2a2a, #1a1a1a)',
                windowHeaderText: 'white',
                accent: '#bb86fc',
                isDark: true
            },
            'ubuntu-light': {
                name: 'Ubuntu 浅色',
                background: 'linear-gradient(135deg, #fafafa 0%, #f0f0f0 50%, #e8e8e8 100%)',
                topBar: 'rgba(248, 248, 248, 0.98)',
                topBarText: '#2c2c2c',
                dock: 'linear-gradient(135deg, rgba(248, 248, 248, 0.98) 0%, rgba(240, 240, 240, 0.98) 50%, rgba(235, 235, 235, 0.98) 100%)',
                window: 'rgba(255, 255, 255, 0.98)',
                windowText: '#2c2c2c',
                windowHeader: 'linear-gradient(135deg, #f8f8f8, #f0f0f0)',
                windowHeaderText: '#2c2c2c',
                accent: '#1976d2',
                isDark: false
            },
            'ubuntu-purple': {
                name: 'Ubuntu 紫色',
                background: 'linear-gradient(135deg, #6a1b9a 0%, #4a148c 50%, #2e0854 100%)',
                topBar: 'rgba(46, 8, 84, 0.95)',
                topBarText: 'white',
                dock: 'linear-gradient(135deg, rgba(106, 27, 154, 0.95) 0%, rgba(74, 20, 140, 0.95) 50%, rgba(46, 8, 84, 0.95) 100%)',
                window: 'rgba(255, 255, 255, 0.95)',
                windowText: '#2e0854',
                windowHeader: 'linear-gradient(135deg, #6a1b9a, #4a148c)',
                windowHeaderText: 'white',
                accent: '#9c27b0',
                isDark: false
            },
            'ubuntu-green': {
                name: 'Ubuntu 绿色',
                background: 'linear-gradient(135deg, #2e7d32 0%, #1b5e20 50%, #0d3f14 100%)',
                topBar: 'rgba(13, 63, 20, 0.95)',
                topBarText: 'white',
                dock: 'linear-gradient(135deg, rgba(46, 125, 50, 0.95) 0%, rgba(27, 94, 32, 0.95) 50%, rgba(13, 63, 20, 0.95) 100%)',
                window: 'rgba(255, 255, 255, 0.95)',
                windowText: '#0d3f14',
                windowHeader: 'linear-gradient(135deg, #2e7d32, #1b5e20)',
                windowHeaderText: 'white',
                accent: '#4caf50',
                isDark: false
            },
            'ubuntu-blue': {
                name: 'Ubuntu 蓝色',
                background: 'linear-gradient(135deg, #1976d2 0%, #0d47a1 50%, #0a2e5c 100%)',
                topBar: 'rgba(10, 46, 92, 0.95)',
                topBarText: 'white',
                dock: 'linear-gradient(135deg, rgba(25, 118, 210, 0.95) 0%, rgba(13, 71, 161, 0.95) 50%, rgba(10, 46, 92, 0.95) 100%)',
                window: 'rgba(255, 255, 255, 0.95)',
                windowText: '#0a2e5c',
                windowHeader: 'linear-gradient(135deg, #1976d2, #0d47a1)',
                windowHeaderText: 'white',
                accent: '#2196f3',
                isDark: false
            }
        },

        init() {
            this.loadSavedTheme();
            this.setupEventListeners();
        },

        loadSavedTheme() {
            const savedTheme = localStorage.getItem('ubuntu-theme');
            if (savedTheme && this.themes[savedTheme]) {
                this.currentTheme = savedTheme;
                this.applyTheme(savedTheme);
                this.updateActiveOption(savedTheme);
            }
        },

        setupEventListeners() {
            const themeSwitcher = document.getElementById('theme-switcher');
            const themePanel = document.getElementById('theme-panel');
            const themePanelClose = document.getElementById('theme-panel-close');
            const themeOptions = document.querySelectorAll('.theme-option');

            themeSwitcher.addEventListener('click', () => {
                themePanel.classList.toggle('active');
            });

            themePanelClose.addEventListener('click', () => {
                themePanel.classList.remove('active');
            });

            // Close panel when clicking outside
            themePanel.addEventListener('click', (e) => {
                if (e.target === themePanel) {
                    themePanel.classList.remove('active');
                }
            });

            themeOptions.forEach(option => {
                option.addEventListener('click', () => {
                    const themeName = option.getAttribute('data-theme');
                    this.switchTheme(themeName);
                    themePanel.classList.remove('active');
                });
            });
        },

        switchTheme(themeName) {
            if (!this.themes[themeName]) return;

            this.currentTheme = themeName;
            this.applyTheme(themeName);
            this.updateActiveOption(themeName);
            localStorage.setItem('ubuntu-theme', themeName);
        },

        applyTheme(themeName) {
            const theme = this.themes[themeName];
            const root = document.documentElement;

            // Apply CSS custom properties
            root.style.setProperty('--theme-background', theme.background);
            root.style.setProperty('--theme-top-bar', theme.topBar);
            root.style.setProperty('--theme-top-bar-text', theme.topBarText);
            root.style.setProperty('--theme-dock', theme.dock);
            root.style.setProperty('--theme-window', theme.window);
            root.style.setProperty('--theme-window-text', theme.windowText);
            root.style.setProperty('--theme-window-header', theme.windowHeader);
            root.style.setProperty('--theme-window-header-text', theme.windowHeaderText);
            root.style.setProperty('--theme-accent', theme.accent);

            // Apply to body background
            document.body.style.background = theme.background;

            // Apply to top bar
            const topBar = document.querySelector('.top-bar');
            if (topBar) {
                topBar.style.background = theme.topBar;
                topBar.style.color = theme.topBarText;
            }

            // Apply to dock
            const dockContainer = document.querySelector('.dock-container');
            if (dockContainer) {
                dockContainer.style.background = theme.dock;
            }

            // Apply to windows
            const windows = document.querySelectorAll('.app-window');
            windows.forEach(window => {
                window.style.background = theme.window;
                window.style.color = theme.windowText;

                const header = window.querySelector('.ubuntu-header');
                if (header) {
                    header.style.background = theme.windowHeader;
                    header.style.color = theme.windowHeaderText;

                    // Update window title specifically
                    const windowTitle = header.querySelector('.window-title');
                    if (windowTitle) {
                        windowTitle.style.color = theme.windowHeaderText;
                    }
                }

                // Update window content text colors
                const windowContent = window.querySelector('.window-content');
                if (windowContent) {
                    windowContent.style.color = theme.windowText;
                }

                // Update card text colors
                const cards = window.querySelectorAll('.ubuntu-card');
                cards.forEach(card => {
                    card.style.color = theme.windowText;

                    // Update headings
                    const headings = card.querySelectorAll('h1, h2, h3, h4, h5, h6');
                    headings.forEach(heading => {
                        if (theme.isDark) {
                            heading.style.color = 'white';
                        } else {
                            heading.style.color = theme.windowText;
                        }
                    });

                    // Update paragraphs and text
                    const paragraphs = card.querySelectorAll('p, .contact-item, .solution-info p, .card-content p');
                    paragraphs.forEach(p => {
                        if (theme.isDark) {
                            p.style.color = 'rgba(255, 255, 255, 0.8)';
                        } else {
                            p.style.color = '#666';
                        }
                    });

                    // Update strong elements
                    const strongs = card.querySelectorAll('strong');
                    strongs.forEach(strong => {
                        strong.style.color = theme.windowText;
                    });
                });
            });

            // Update desktop background
            const desktopWallpaper = document.querySelector('.desktop-wallpaper');
            if (desktopWallpaper) {
                if (theme.isDark || themeName === 'ubuntu-default') {
                    desktopWallpaper.style.background = `linear-gradient(135deg,
                        rgba(233, 84, 32, 0.1) 0%,
                        rgba(119, 41, 83, 0.1) 50%,
                        rgba(44, 0, 30, 0.1) 100%)`;
                } else {
                    // 浅色主题使用纯色背景
                    desktopWallpaper.style.background = `linear-gradient(135deg,
                        rgba(250, 250, 250, 0.8) 0%,
                        rgba(245, 245, 245, 0.8) 50%,
                        rgba(240, 240, 240, 0.8) 100%)`;
                }
            }

            // Update welcome text colors
            const welcomeText = document.querySelector('.welcome-text');
            if (welcomeText) {
                const h1 = welcomeText.querySelector('h1');
                const p = welcomeText.querySelector('p');
                if (theme.isDark || themeName === 'ubuntu-default') {
                    if (h1) h1.style.color = 'rgba(255, 255, 255, 0.9)';
                    if (p) p.style.color = 'rgba(255, 255, 255, 0.7)';
                } else {
                    if (h1) h1.style.color = 'rgba(44, 44, 44, 0.9)';
                    if (p) p.style.color = 'rgba(44, 44, 44, 0.7)';
                }
            }

            // Update active dock item colors
            this.updateDockActiveColors(theme.accent);

            // Update dock label colors for light themes
            this.updateDockLabelColors(theme.isDark);
        },

        updateDockActiveColors(accentColor) {
            const style = document.createElement('style');
            style.id = 'theme-dock-active-style';

            // Remove existing theme style
            const existingStyle = document.getElementById('theme-dock-active-style');
            if (existingStyle) {
                existingStyle.remove();
            }

            style.textContent = `
                .dock-item.active {
                    background: linear-gradient(135deg,
                        ${accentColor}30 0%,
                        ${accentColor}20 100%) !important;
                    border: 1px solid ${accentColor}99 !important;
                    box-shadow: 0 4px 12px ${accentColor}4d !important;
                }
                .dock-item.active::after {
                    background: ${accentColor} !important;
                    box-shadow: 0 2px 4px ${accentColor}80 !important;
                }
            `;

            document.head.appendChild(style);
        },

        updateDockLabelColors(isDark) {
            const style = document.createElement('style');
            style.id = 'theme-dock-label-style';

            // Remove existing label style
            const existingStyle = document.getElementById('theme-dock-label-style');
            if (existingStyle) {
                existingStyle.remove();
            }

            if (isDark) {
                style.textContent = `
                    .dock-label {
                        background: linear-gradient(135deg,
                            rgba(40, 40, 40, 0.95) 0%,
                            rgba(20, 20, 20, 0.95) 100%) !important;
                        color: white !important;
                        border: 1px solid rgba(255, 255, 255, 0.15) !important;
                    }
                `;
            } else {
                style.textContent = `
                    .dock-label {
                        background: linear-gradient(135deg,
                            rgba(248, 248, 248, 0.98) 0%,
                            rgba(240, 240, 240, 0.98) 100%) !important;
                        color: #2c2c2c !important;
                        border: 1px solid rgba(0, 0, 0, 0.12) !important;
                        box-shadow:
                            0 8px 24px rgba(0, 0, 0, 0.15),
                            inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;
                    }
                `;
            }

            document.head.appendChild(style);
        },

        updateActiveOption(themeName) {
            const themeOptions = document.querySelectorAll('.theme-option');
            themeOptions.forEach(option => {
                option.classList.remove('active');
                if (option.getAttribute('data-theme') === themeName) {
                    option.classList.add('active');
                }
            });
        }
    };

    // Initialize theme system
    themeSystem.init();
    
    // Clock update
    function updateClock() {
        const now = new Date();
        const options = { 
            month: 'long', 
            day: 'numeric',
            hour: '2-digit', 
            minute: '2-digit',
            hour12: false 
        };
        const timeString = now.toLocaleDateString('zh-CN', options);
        const clockElement = document.querySelector('.clock');
        if (clockElement) {
            clockElement.textContent = timeString.replace('年', '年 ');
        }
    }
    
    // Update clock every minute
    updateClock();
    setInterval(updateClock, 60000);
    
    // Smooth scrolling for content areas
    const contentAreas = document.querySelectorAll('.window-content');
    
    contentAreas.forEach(area => {
        area.style.scrollBehavior = 'smooth';
    });
    
    // Add loading animation on page load
    document.body.style.opacity = '0';
    document.body.style.transition = 'opacity 0.5s ease';
    
    setTimeout(() => {
        document.body.style.opacity = '1';
    }, 100);
    
    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Alt + number keys for navigation
        if (e.altKey) {
            const keyMap = {
                '1': 'home',
                '2': 'products', 
                '3': 'solutions',
                '4': 'about',
                '5': 'contact'
            };
            
            const targetView = keyMap[e.key];
            if (targetView) {
                e.preventDefault();
                const targetNav = document.querySelector(`[data-view="${targetView}"]`);
                if (targetNav) {
                    targetNav.click();
                }
            }
        }
    });
    
    // Auto-open home window on load
    setTimeout(() => {
        openWindow('home');
    }, 500);

    // Global window control functions
    window.windowClose = function(appName) {
        console.log('Closing window:', appName);
        closeWindow(appName);
    };

    window.windowMinimize = function(appName) {
        console.log('Minimizing window:', appName);
        minimizeWindow(appName);
    };

    window.windowMaximize = function(appName) {
        console.log('Toggling maximize for window:', appName);
        toggleMaximize(appName);
    };

    // Legacy test functions
    window.testClose = window.windowClose;
    window.testMinimize = window.windowMinimize;
    window.testMaximize = window.windowMaximize;

    console.log('Ubuntu style website loaded successfully! 🚀');
    console.log('Keyboard shortcuts: Alt + 1-5 for navigation');
    console.log('Window controls: Drag to move, click controls to minimize/maximize/close');
    console.log('Test functions: testClose("home"), testMinimize("home"), testMaximize("home")');
});
