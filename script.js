// ==================== 科技感增强效果 ====================

// 粒子系统
class ParticleSystem {
    constructor() {
        this.canvas = document.createElement('canvas');
        this.ctx = this.canvas.getContext('2d');
        this.particles = [];
        this.mouse = { x: 0, y: 0 };

        this.setupCanvas();
        this.createParticles();
        this.bindEvents();
        this.animate();
    }

    setupCanvas() {
        this.canvas.style.position = 'fixed';
        this.canvas.style.top = '0';
        this.canvas.style.left = '0';
        this.canvas.style.width = '100%';
        this.canvas.style.height = '100%';
        this.canvas.style.pointerEvents = 'none';
        this.canvas.style.zIndex = '1';
        this.canvas.style.opacity = '0.6';
        document.body.appendChild(this.canvas);

        this.resize();
    }

    resize() {
        this.canvas.width = window.innerWidth;
        this.canvas.height = window.innerHeight;
    }

    createParticles() {
        const particleCount = Math.floor((window.innerWidth * window.innerHeight) / 15000);
        for (let i = 0; i < particleCount; i++) {
            this.particles.push({
                x: Math.random() * this.canvas.width,
                y: Math.random() * this.canvas.height,
                vx: (Math.random() - 0.5) * 0.5,
                vy: (Math.random() - 0.5) * 0.5,
                size: Math.random() * 2 + 1,
                opacity: Math.random() * 0.5 + 0.2,
                pulse: Math.random() * Math.PI * 2
            });
        }
    }

    bindEvents() {
        window.addEventListener('resize', () => this.resize());
        window.addEventListener('mousemove', (e) => {
            this.mouse.x = e.clientX;
            this.mouse.y = e.clientY;
        });
    }

    animate() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        this.particles.forEach((particle, index) => {
            // 更新粒子位置
            particle.x += particle.vx;
            particle.y += particle.vy;
            particle.pulse += 0.02;

            // 边界检测
            if (particle.x < 0 || particle.x > this.canvas.width) particle.vx *= -1;
            if (particle.y < 0 || particle.y > this.canvas.height) particle.vy *= -1;

            // 鼠标交互
            const dx = this.mouse.x - particle.x;
            const dy = this.mouse.y - particle.y;
            const distance = Math.sqrt(dx * dx + dy * dy);

            if (distance < 100) {
                const force = (100 - distance) / 100;
                particle.vx += dx * force * 0.001;
                particle.vy += dy * force * 0.001;
            }

            // 绘制粒子
            const pulseSize = particle.size + Math.sin(particle.pulse) * 0.5;
            this.ctx.beginPath();
            this.ctx.arc(particle.x, particle.y, pulseSize, 0, Math.PI * 2);
            this.ctx.fillStyle = `rgba(0, 122, 255, ${particle.opacity})`;
            this.ctx.fill();

            // 连接线
            this.particles.forEach((otherParticle, otherIndex) => {
                if (index !== otherIndex) {
                    const dx = particle.x - otherParticle.x;
                    const dy = particle.y - otherParticle.y;
                    const distance = Math.sqrt(dx * dx + dy * dy);

                    if (distance < 80) {
                        this.ctx.beginPath();
                        this.ctx.moveTo(particle.x, particle.y);
                        this.ctx.lineTo(otherParticle.x, otherParticle.y);
                        this.ctx.strokeStyle = `rgba(0, 122, 255, ${0.1 * (1 - distance / 80)})`;
                        this.ctx.lineWidth = 0.5;
                        this.ctx.stroke();
                    }
                }
            });
        });

        requestAnimationFrame(() => this.animate());
    }
}

// 数字雨效果
class DigitalRain {
    constructor() {
        this.canvas = document.createElement('canvas');
        this.ctx = this.canvas.getContext('2d');
        this.drops = [];

        this.setupCanvas();
        this.createDrops();
        this.animate();
    }

    setupCanvas() {
        this.canvas.style.position = 'fixed';
        this.canvas.style.top = '0';
        this.canvas.style.left = '0';
        this.canvas.style.width = '100%';
        this.canvas.style.height = '100%';
        this.canvas.style.pointerEvents = 'none';
        this.canvas.style.zIndex = '0';
        this.canvas.style.opacity = '0.1';
        document.body.appendChild(this.canvas);

        this.resize();
    }

    resize() {
        this.canvas.width = window.innerWidth;
        this.canvas.height = window.innerHeight;
    }

    createDrops() {
        const columns = Math.floor(this.canvas.width / 20);
        for (let i = 0; i < columns; i++) {
            this.drops.push({
                x: i * 20,
                y: Math.random() * this.canvas.height,
                speed: Math.random() * 3 + 1,
                chars: '01'.split(''),
                currentChar: 0
            });
        }
    }

    animate() {
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        this.ctx.fillStyle = '#00ff00';
        this.ctx.font = '14px monospace';

        this.drops.forEach(drop => {
            const char = drop.chars[Math.floor(Math.random() * drop.chars.length)];
            this.ctx.fillText(char, drop.x, drop.y);

            drop.y += drop.speed;

            if (drop.y > this.canvas.height && Math.random() > 0.975) {
                drop.y = 0;
            }
        });

        requestAnimationFrame(() => this.animate());
    }
}

// 全息扫描线效果
class HolographicScanner {
    constructor() {
        this.scanLine = document.createElement('div');
        this.setupScanLine();
        this.animate();
    }

    setupScanLine() {
        this.scanLine.style.position = 'fixed';
        this.scanLine.style.top = '0';
        this.scanLine.style.left = '0';
        this.scanLine.style.width = '100%';
        this.scanLine.style.height = '2px';
        this.scanLine.style.background = 'linear-gradient(90deg, transparent, #00ff00, transparent)';
        this.scanLine.style.boxShadow = '0 0 20px #00ff00';
        this.scanLine.style.pointerEvents = 'none';
        this.scanLine.style.zIndex = '999';
        this.scanLine.style.opacity = '0.7';
        document.body.appendChild(this.scanLine);
    }

    animate() {
        let position = 0;
        const speed = 2;

        const scan = () => {
            position += speed;
            if (position > window.innerHeight + 100) {
                position = -100;
            }

            this.scanLine.style.top = position + 'px';
            requestAnimationFrame(scan);
        };

        scan();
    }
}

// 鼠标跟随光晕效果
class MouseGlow {
    constructor() {
        this.glow = document.createElement('div');
        this.setupGlow();
        this.bindEvents();
    }

    setupGlow() {
        this.glow.style.position = 'fixed';
        this.glow.style.width = '300px';
        this.glow.style.height = '300px';
        this.glow.style.borderRadius = '50%';
        this.glow.style.background = 'radial-gradient(circle, rgba(0,122,255,0.1) 0%, transparent 70%)';
        this.glow.style.pointerEvents = 'none';
        this.glow.style.zIndex = '2';
        this.glow.style.transform = 'translate(-50%, -50%)';
        this.glow.style.transition = 'opacity 0.3s ease';
        this.glow.style.opacity = '0';
        document.body.appendChild(this.glow);
    }

    bindEvents() {
        document.addEventListener('mousemove', (e) => {
            this.glow.style.left = e.clientX + 'px';
            this.glow.style.top = e.clientY + 'px';
            this.glow.style.opacity = '1';
        });

        document.addEventListener('mouseleave', () => {
            this.glow.style.opacity = '0';
        });
    }
}

// 脉冲动画增强
class PulseEnhancer {
    constructor() {
        this.addPulseToElements();
    }

    addPulseToElements() {
        // 为按钮添加脉冲效果
        document.querySelectorAll('.btn-primary').forEach(btn => {
            btn.addEventListener('mouseenter', () => {
                btn.style.animation = 'pulse 1s infinite';
            });

            btn.addEventListener('mouseleave', () => {
                btn.style.animation = '';
            });
        });

        // 为产品卡片添加悬停脉冲
        document.querySelectorAll('.product-card').forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.boxShadow = '0 0 30px rgba(0, 122, 255, 0.3)';
                card.style.transform = 'translateY(-8px) scale(1.02)';
            });

            card.addEventListener('mouseleave', () => {
                card.style.boxShadow = '';
                card.style.transform = '';
            });
        });
    }
}

// 3D 倾斜效果
class TiltEffect {
    constructor() {
        this.addTiltToCards();
    }

    addTiltToCards() {
        document.querySelectorAll('.product-card').forEach(card => {
            card.addEventListener('mousemove', (e) => {
                const rect = card.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                const centerX = rect.width / 2;
                const centerY = rect.height / 2;

                const rotateX = (y - centerY) / 10;
                const rotateY = (centerX - x) / 10;

                card.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateY(-8px)`;
            });

            card.addEventListener('mouseleave', () => {
                card.style.transform = '';
            });
        });
    }
}

// 打字机效果
class TypewriterEffect {
    constructor(element, text, speed = 100) {
        this.element = element;
        this.text = text;
        this.speed = speed;
        this.index = 0;

        this.type();
    }

    type() {
        if (this.index < this.text.length) {
            this.element.textContent += this.text.charAt(this.index);
            this.index++;
            setTimeout(() => this.type(), this.speed);
        }
    }
}

// 滚动视差效果
class ParallaxEffect {
    constructor() {
        this.bindScrollEvents();
    }

    bindScrollEvents() {
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const parallaxElements = document.querySelectorAll('.floating-elements .element');

            parallaxElements.forEach((element, index) => {
                const speed = 0.5 + (index * 0.1);
                element.style.transform = `translateY(${scrolled * speed}px)`;
            });
        });
    }
}

// ==================== 原有功能保留 ====================

// Smooth scrolling for navigation links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Navbar background on scroll with enhanced effect
window.addEventListener('scroll', () => {
    const navbar = document.querySelector('.navbar');
    const scrolled = window.scrollY;

    if (scrolled > 50) {
        navbar.style.background = 'rgba(255, 255, 255, 0.95)';
        navbar.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
    } else {
        navbar.style.background = 'rgba(255, 255, 255, 0.8)';
        navbar.style.boxShadow = 'none';
    }
});

// Enhanced Intersection Observer for animations
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.style.opacity = '1';
            entry.target.style.transform = 'translateY(0)';
            entry.target.classList.add('animate-in');
        }
    });
}, observerOptions);

// Observe elements for animation with stagger effect
document.querySelectorAll('.product-card, .section-title').forEach((el, index) => {
    el.style.opacity = '0';
    el.style.transform = 'translateY(30px)';
    el.style.transition = `opacity 0.6s ease ${index * 0.1}s, transform 0.6s ease ${index * 0.1}s`;
    observer.observe(el);
});

// ==================== 初始化所有效果 ====================

// 页面加载完成后初始化所有科技效果
window.addEventListener('load', () => {
    // 基础加载动画
    document.body.style.opacity = '1';

    // 初始化科技效果（延迟启动以确保页面完全加载）
    setTimeout(() => {
        new ParticleSystem();
        new MouseGlow();
        new PulseEnhancer();
        new TiltEffect();
        new ParallaxEffect();

        // 可选：数字雨效果（可能会影响性能，根据需要启用）
        // new DigitalRain();

        // 可选：全息扫描线（比较炫酷但可能干扰阅读）
        // new HolographicScanner();

        // 为标题添加打字机效果
        const heroTitle = document.querySelector('.hero-title');
        if (heroTitle && heroTitle.textContent) {
            const originalText = heroTitle.textContent;
            heroTitle.textContent = '';
            new TypewriterEffect(heroTitle, originalText, 80);
        }
    }, 500);
});

// 初始化页面透明度
document.body.style.opacity = '0';
document.body.style.transition = 'opacity 0.8s ease';

// 性能优化：在低性能设备上减少效果
const isLowPerformance = () => {
    return navigator.hardwareConcurrency < 4 ||
           /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
};

// 响应式处理
window.addEventListener('resize', () => {
    // 重新计算粒子数量等
    if (window.innerWidth < 768 && isLowPerformance()) {
        document.querySelectorAll('canvas').forEach(canvas => {
            canvas.style.display = 'none';
        });
    }
});