/* Ubuntu Style CSS */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Ubuntu', sans-serif;
    background: linear-gradient(135deg, #e95420 0%, #772953 50%, #2c001e 100%);
    height: 100vh;
    overflow: hidden;
}

/* Ubuntu Desktop */
.ubuntu-desktop {
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Top Bar */
.top-bar {
    height: 32px;
    background: rgba(44, 0, 30, 0.95);
    backdrop-filter: blur(10px);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 12px;
    color: white;
    font-size: 14px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.top-bar-left {
    display: flex;
    align-items: center;
    gap: 8px;
}

.activities-button {
    padding: 4px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.2s;
}

.activities-button:hover {
    background: rgba(255, 255, 255, 0.1);
}

.apps-button {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-radius: 4px;
    transition: background 0.2s;
}

.apps-button:hover {
    background: rgba(255, 255, 255, 0.1);
}

.apps-button.active {
    background: rgba(233, 84, 32, 0.3);
}

.apps-grid-icon {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2px;
    width: 12px;
    height: 12px;
}

.dot {
    width: 2px;
    height: 2px;
    background: white;
    border-radius: 50%;
}

.clock {
    font-weight: 500;
}

.system-indicators {
    display: flex;
    gap: 8px;
}

.indicator {
    padding: 2px 6px;
    border-radius: 3px;
    cursor: pointer;
    transition: background 0.2s;
}

.indicator:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* Desktop Content */
.desktop-content {
    flex: 1;
    display: flex;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
}

/* Ubuntu Applications Menu */
.ubuntu-menu {
    position: fixed;
    left: 20px;
    top: 50px;
    width: 350px;
    height: calc(100vh - 100px);
    background: rgba(44, 0, 30, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: white;
    display: flex;
    flex-direction: column;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    z-index: 100;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
}

.ubuntu-menu.active {
    transform: translateX(0);
}

.menu-header {
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-box input {
    width: 100%;
    padding: 12px 40px 12px 16px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: white;
    font-family: 'Ubuntu', sans-serif;
    font-size: 14px;
}

.search-box input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.search-box input:focus {
    outline: none;
    border-color: #e95420;
    background: rgba(255, 255, 255, 0.15);
}

.search-icon {
    position: absolute;
    right: 12px;
    color: rgba(255, 255, 255, 0.6);
}

.menu-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

.menu-section {
    margin-bottom: 24px;
}

.section-title {
    font-size: 12px;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.6);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 12px;
}

.app-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
}

.app-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 16px 8px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;
    text-align: center;
}

.app-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.app-item.active {
    background: rgba(233, 84, 32, 0.2);
}

.app-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    margin-bottom: 8px;
    background: linear-gradient(135deg, #e95420, #ff6b35);
    box-shadow: 0 4px 12px rgba(233, 84, 32, 0.3);
}

.app-name {
    font-size: 12px;
    font-weight: 400;
    color: rgba(255, 255, 255, 0.9);
}

.menu-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.company-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.company-logo-small {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #e95420, #ff6b35);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 14px;
    color: white;
}

.company-details {
    flex: 1;
}

.company-name {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 2px;
}

.company-tagline {
    font-size: 11px;
    color: rgba(255, 255, 255, 0.6);
}

/* Mac Style Dock */
.mac-dock {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
}

.dock-container {
    display: flex;
    align-items: end;
    gap: 8px;
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.dock-item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    transform-origin: bottom;
}

.dock-item:hover {
    transform: scale(1.2) translateY(-8px);
}

.dock-item:hover .dock-label {
    opacity: 1;
    transform: translateY(-8px);
}

.dock-item.active .dock-icon {
    background: rgba(233, 84, 32, 0.3);
    border: 2px solid #e95420;
}

.dock-icon {
    width: 56px;
    height: 56px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.2s;
}

.dock-label {
    position: absolute;
    bottom: 70px;
    background: rgba(44, 0, 30, 0.9);
    color: white;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;
    opacity: 0;
    transform: translateY(0);
    transition: all 0.2s;
    pointer-events: none;
}

.dock-separator {
    width: 1px;
    height: 40px;
    background: rgba(255, 255, 255, 0.3);
    margin: 0 4px;
}

/* Main Content */
.main-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    margin-bottom: 100px; /* Space for dock */
}

.content-view {
    display: none;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
}

.content-view.active {
    display: block;
}

/* Window Header */
.window-header {
    height: 40px;
    background: linear-gradient(135deg, #f6f6f6, #e8e8e8);
    border-bottom: 1px solid #d0d0d0;
    display: flex;
    align-items: center;
    padding: 0 16px;
    gap: 12px;
}

.window-controls {
    display: flex;
    gap: 8px;
}

.control {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    cursor: pointer;
}

.control.close {
    background: #ff5f56;
}

.control.minimize {
    background: #ffbd2e;
}

.control.maximize {
    background: #27ca3f;
}

.window-title {
    font-weight: 500;
    color: #333;
    font-size: 14px;
}

/* Window Content */
.window-content {
    padding: 24px;
    height: calc(100vh - 120px);
    overflow-y: auto;
}

/* Ubuntu Cards */
.ubuntu-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 0, 0, 0.05);
    margin-bottom: 16px;
}

/* Hero Section */
.hero-section {
    display: flex;
    align-items: center;
    gap: 40px;
    min-height: 400px;
}

.hero-text {
    flex: 1;
}

.hero-text h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c001e;
    margin-bottom: 8px;
}

.hero-text h2 {
    font-size: 1.5rem;
    font-weight: 400;
    color: #e95420;
    margin-bottom: 16px;
}

.hero-text p {
    font-size: 1.1rem;
    color: #666;
    line-height: 1.6;
    margin-bottom: 24px;
}

.action-buttons {
    display: flex;
    gap: 12px;
}

.ubuntu-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-family: 'Ubuntu', sans-serif;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.ubuntu-btn.primary {
    background: #e95420;
    color: white;
}

.ubuntu-btn.primary:hover {
    background: #d44815;
    transform: translateY(-1px);
}

.ubuntu-btn.secondary {
    background: transparent;
    color: #e95420;
    border: 2px solid #e95420;
}

.ubuntu-btn.secondary:hover {
    background: #e95420;
    color: white;
}

/* Hero Visual */
.hero-visual {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
}

.ubuntu-logo-large {
    width: 200px;
    height: 200px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.logo-ring {
    position: absolute;
    width: 100%;
    height: 100%;
    border: 4px solid #e95420;
    border-radius: 50%;
    border-top-color: transparent;
    border-right-color: transparent;
    animation: rotate 10s linear infinite;
}

.logo-center {
    font-size: 3rem;
    font-weight: 700;
    color: #e95420;
    z-index: 1;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Products Grid */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.product-card {
    transition: transform 0.2s;
}

.product-card:hover {
    transform: translateY(-4px);
}

.card-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
}

.product-icon {
    font-size: 24px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #e95420, #ff6b35);
    border-radius: 8px;
}

.card-header h3 {
    color: #2c001e;
    font-weight: 600;
}

.card-content p {
    color: #666;
    line-height: 1.5;
    margin-bottom: 12px;
}

.features {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.feature-tag {
    background: rgba(233, 84, 32, 0.1);
    color: #e95420;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

/* Solutions */
.solutions-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.solution-item {
    transition: transform 0.2s;
}

.solution-item:hover {
    transform: translateX(4px);
}

.solution-header {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    margin-bottom: 12px;
}

.solution-icon {
    font-size: 24px;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #e95420, #ff6b35);
    border-radius: 8px;
    flex-shrink: 0;
}

.solution-info h3 {
    color: #2c001e;
    font-weight: 600;
    margin-bottom: 4px;
}

.solution-info p {
    color: #666;
    line-height: 1.5;
}

.solution-details ul {
    list-style: none;
    padding-left: 64px;
}

.solution-details li {
    color: #666;
    padding: 4px 0;
    position: relative;
}

.solution-details li::before {
    content: "•";
    color: #e95420;
    position: absolute;
    left: -16px;
}

/* About */
.about-content {
    max-width: 800px;
}

.company-info h3 {
    color: #2c001e;
    font-weight: 600;
    margin-bottom: 16px;
}

.company-info p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 24px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
}

.stat-item {
    text-align: center;
    padding: 20px;
    background: rgba(233, 84, 32, 0.05);
    border-radius: 8px;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #e95420;
    margin-bottom: 4px;
}

.stat-label {
    color: #666;
    font-size: 14px;
}

/* Contact */
.contact-info h3 {
    color: #2c001e;
    font-weight: 600;
    margin-bottom: 16px;
}

.contact-details {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.contact-item {
    color: #666;
    line-height: 1.5;
}

.contact-item strong {
    color: #2c001e;
}

/* Responsive */
@media (max-width: 768px) {
    .ubuntu-menu {
        width: calc(100vw - 40px);
        left: 20px;
        right: 20px;
    }

    .app-grid {
        grid-template-columns: repeat(4, 1fr);
    }

    .dock-container {
        padding: 8px 12px;
        gap: 4px;
    }

    .dock-icon {
        width: 48px;
        height: 48px;
        font-size: 20px;
    }

    .dock-item:hover {
        transform: scale(1.1) translateY(-4px);
    }

    .hero-section {
        flex-direction: column;
        text-align: center;
        padding: 20px 0;
    }

    .hero-text h1 {
        font-size: 2rem;
    }

    .hero-text h2 {
        font-size: 1.2rem;
    }

    .products-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .main-content {
        padding: 15px;
        margin-bottom: 120px;
    }

    .window-content {
        padding: 16px;
    }
}

@media (max-width: 480px) {
    .top-bar {
        padding: 0 8px;
    }

    .ubuntu-menu {
        width: calc(100vw - 20px);
        left: 10px;
        right: 10px;
        height: calc(100vh - 80px);
    }

    .app-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 12px;
    }

    .dock-container {
        flex-wrap: wrap;
        justify-content: center;
        max-width: calc(100vw - 40px);
    }

    .dock-separator {
        display: none;
    }
}
