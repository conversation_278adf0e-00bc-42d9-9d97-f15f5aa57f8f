/* Ubuntu Style CSS */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Ubuntu', sans-serif;
    background: linear-gradient(135deg, #e95420 0%, #772953 50%, #2c001e 100%);
    height: 100vh;
    overflow: hidden;
}

/* Ubuntu Desktop */
.ubuntu-desktop {
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Top Bar */
.top-bar {
    height: 32px;
    background: rgba(44, 0, 30, 0.95);
    backdrop-filter: blur(10px);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 12px;
    color: white;
    font-size: 14px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.activities-button {
    padding: 4px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
}

.activities-button:hover {
    background: rgba(255, 255, 255, 0.1);
}

.activities-button.active {
    background: rgba(233, 84, 32, 0.3);
    color: #e95420;
}

/* Activities Overview Mode */
.ubuntu-desktop.overview-mode {
    background: rgba(0, 0, 0, 0.8);
}

.ubuntu-desktop.overview-mode .desktop-content {
    background: rgba(0, 0, 0, 0.5);
}

.app-window.overview-window {
    transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    cursor: pointer;
    border: 2px solid rgba(233, 84, 32, 0.5);
}

.app-window.overview-window:hover {
    border-color: #e95420;
    box-shadow: 0 8px 32px rgba(233, 84, 32, 0.3);
}

.clock {
    font-weight: 500;
}

.system-indicators {
    display: flex;
    gap: 8px;
}

.indicator {
    padding: 2px 6px;
    border-radius: 3px;
    cursor: pointer;
    transition: background 0.2s;
}

.indicator:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* Desktop Content */
.desktop-content {
    flex: 1;
    display: flex;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
}

/* Remove Ubuntu Applications Menu styles - no longer needed */

/* Mac Style Dock */
.mac-dock {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
}

.dock-container {
    display: flex;
    align-items: end;
    gap: 8px;
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.dock-item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    transform-origin: bottom;
}

.dock-item:hover {
    transform: scale(1.2) translateY(-8px);
}

.dock-item:hover .dock-label {
    opacity: 1;
    transform: translateY(-8px);
}

.dock-item.active .dock-icon {
    background: rgba(233, 84, 32, 0.3);
    border: 2px solid #e95420;
}

.dock-icon {
    width: 56px;
    height: 56px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.2s;
}

.dock-label {
    position: absolute;
    bottom: 70px;
    background: rgba(44, 0, 30, 0.9);
    color: white;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;
    opacity: 0;
    transform: translateY(0);
    transition: all 0.2s;
    pointer-events: none;
}

.dock-separator {
    width: 1px;
    height: 40px;
    background: rgba(255, 255, 255, 0.3);
    margin: 0 4px;
}

/* Taskbar for minimized windows */
.taskbar {
    position: fixed;
    top: 32px;
    left: 0;
    right: 0;
    height: 40px;
    background: rgba(44, 0, 30, 0.9);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    padding: 0 12px;
    z-index: 5;
}

.taskbar-items {
    display: flex;
    gap: 8px;
}

.taskbar-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    color: white;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;
    max-width: 200px;
}

.taskbar-item:hover {
    background: rgba(255, 255, 255, 0.2);
}

.taskbar-item.active {
    background: rgba(233, 84, 32, 0.3);
    border: 1px solid rgba(233, 84, 32, 0.5);
}

.taskbar-icon {
    font-size: 16px;
}

.taskbar-title {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Window dragging */
.app-window.dragging {
    user-select: none;
    pointer-events: none;
}

.window-header {
    cursor: move;
}

/* Window animations */
.app-window {
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.app-window.opening {
    animation: windowOpen 0.3s ease-out;
}

.app-window.closing {
    animation: windowClose 0.3s ease-in;
}

@keyframes windowOpen {
    from {
        opacity: 0;
        transform: scale(0.8) translateY(20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

@keyframes windowClose {
    from {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
    to {
        opacity: 0;
        transform: scale(0.8) translateY(20px);
    }
}

/* Main Content - Desktop Background */
.main-content {
    flex: 1;
    position: relative;
    overflow: hidden;
}

.desktop-background {
    width: 100%;
    height: 100%;
    position: relative;
}

.desktop-wallpaper {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
        rgba(233, 84, 32, 0.1) 0%,
        rgba(119, 41, 83, 0.1) 50%,
        rgba(44, 0, 30, 0.1) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.welcome-text h1 {
    font-size: 3rem;
    font-weight: 300;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 16px;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.welcome-text p {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.7);
    text-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
}

/* App Windows */
.app-window {
    position: fixed;
    width: 800px;
    height: 600px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
    z-index: 10;
    display: none;
    resize: both;
    min-width: 400px;
    min-height: 300px;
    max-width: 90vw;
    max-height: 90vh;
}

.app-window.active {
    display: flex;
    flex-direction: column;
}

.app-window.minimized {
    display: none;
}

.app-window.maximized {
    width: 100vw !important;
    height: calc(100vh - 32px) !important;
    top: 32px !important;
    left: 0 !important;
    border-radius: 0;
    resize: none;
}

/* Window positioning */
.app-window[data-app="home"] {
    top: 80px;
    left: 100px;
}

.app-window[data-app="products"] {
    top: 120px;
    left: 140px;
}

.app-window[data-app="solutions"] {
    top: 160px;
    left: 180px;
}

.app-window[data-app="about"] {
    top: 200px;
    left: 220px;
}

.app-window[data-app="contact"] {
    top: 240px;
    left: 260px;
}

/* Ubuntu Window Header */
.ubuntu-header {
    height: 40px;
    background: linear-gradient(135deg, #3c3c3c, #2c2c2c);
    border-bottom: 1px solid #1a1a1a;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    color: white;
}

.window-title {
    font-weight: 500;
    font-size: 14px;
    color: white;
    flex: 1;
    text-align: center;
    margin: 0 60px; /* Space for controls */
}

.ubuntu-controls {
    display: flex;
    gap: 8px;
}

.ubuntu-control {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
    color: rgba(255, 255, 255, 0.7);
    pointer-events: auto;
    z-index: 10;
    position: relative;
}

.ubuntu-control:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.ubuntu-control.minimize:hover {
    background: rgba(255, 193, 7, 0.2);
    color: #ffc107;
}

.ubuntu-control.maximize:hover {
    background: rgba(40, 167, 69, 0.2);
    color: #28a745;
}

.ubuntu-control.close:hover {
    background: rgba(220, 53, 69, 0.2);
    color: #dc3545;
}

.ubuntu-control svg {
    width: 16px;
    height: 16px;
    pointer-events: none;
}

/* Legacy control styles - remove */
.control {
    display: none;
}

/* Window Content */
.window-content {
    padding: 24px;
    height: calc(100vh - 120px);
    overflow-y: auto;
}

/* Ubuntu Cards */
.ubuntu-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 0, 0, 0.05);
    margin-bottom: 16px;
}

/* Hero Section */
.hero-section {
    display: flex;
    align-items: center;
    gap: 40px;
    min-height: 400px;
}

.hero-text {
    flex: 1;
}

.hero-text h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c001e;
    margin-bottom: 8px;
}

.hero-text h2 {
    font-size: 1.5rem;
    font-weight: 400;
    color: #e95420;
    margin-bottom: 16px;
}

.hero-text p {
    font-size: 1.1rem;
    color: #666;
    line-height: 1.6;
    margin-bottom: 24px;
}

.action-buttons {
    display: flex;
    gap: 12px;
}

.ubuntu-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-family: 'Ubuntu', sans-serif;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.ubuntu-btn.primary {
    background: #e95420;
    color: white;
}

.ubuntu-btn.primary:hover {
    background: #d44815;
    transform: translateY(-1px);
}

.ubuntu-btn.secondary {
    background: transparent;
    color: #e95420;
    border: 2px solid #e95420;
}

.ubuntu-btn.secondary:hover {
    background: #e95420;
    color: white;
}

/* Hero Visual */
.hero-visual {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
}

.ubuntu-logo-large {
    width: 200px;
    height: 200px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.logo-ring {
    position: absolute;
    width: 100%;
    height: 100%;
    border: 4px solid #e95420;
    border-radius: 50%;
    border-top-color: transparent;
    border-right-color: transparent;
    animation: rotate 10s linear infinite;
}

.logo-center {
    font-size: 3rem;
    font-weight: 700;
    color: #e95420;
    z-index: 1;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Products Grid */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.product-card {
    transition: transform 0.2s;
}

.product-card:hover {
    transform: translateY(-4px);
}

.card-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
}

.product-icon {
    font-size: 24px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #e95420, #ff6b35);
    border-radius: 8px;
}

.card-header h3 {
    color: #2c001e;
    font-weight: 600;
}

.card-content p {
    color: #666;
    line-height: 1.5;
    margin-bottom: 12px;
}

.features {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.feature-tag {
    background: rgba(233, 84, 32, 0.1);
    color: #e95420;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

/* Solutions */
.solutions-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.solution-item {
    transition: transform 0.2s;
}

.solution-item:hover {
    transform: translateX(4px);
}

.solution-header {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    margin-bottom: 12px;
}

.solution-icon {
    font-size: 24px;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #e95420, #ff6b35);
    border-radius: 8px;
    flex-shrink: 0;
}

.solution-info h3 {
    color: #2c001e;
    font-weight: 600;
    margin-bottom: 4px;
}

.solution-info p {
    color: #666;
    line-height: 1.5;
}

.solution-details ul {
    list-style: none;
    padding-left: 64px;
}

.solution-details li {
    color: #666;
    padding: 4px 0;
    position: relative;
}

.solution-details li::before {
    content: "•";
    color: #e95420;
    position: absolute;
    left: -16px;
}

/* About */
.about-content {
    max-width: 800px;
}

.company-info h3 {
    color: #2c001e;
    font-weight: 600;
    margin-bottom: 16px;
}

.company-info p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 24px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
}

.stat-item {
    text-align: center;
    padding: 20px;
    background: rgba(233, 84, 32, 0.05);
    border-radius: 8px;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #e95420;
    margin-bottom: 4px;
}

.stat-label {
    color: #666;
    font-size: 14px;
}

/* Contact */
.contact-info h3 {
    color: #2c001e;
    font-weight: 600;
    margin-bottom: 16px;
}

.contact-details {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.contact-item {
    color: #666;
    line-height: 1.5;
}

.contact-item strong {
    color: #2c001e;
}

/* Responsive */
@media (max-width: 768px) {
    .ubuntu-menu {
        width: calc(100vw - 40px);
        left: 20px;
        right: 20px;
    }

    .app-grid {
        grid-template-columns: repeat(4, 1fr);
    }

    .dock-container {
        padding: 8px 12px;
        gap: 4px;
    }

    .dock-icon {
        width: 48px;
        height: 48px;
        font-size: 20px;
    }

    .dock-item:hover {
        transform: scale(1.1) translateY(-4px);
    }

    .hero-section {
        flex-direction: column;
        text-align: center;
        padding: 20px 0;
    }

    .hero-text h1 {
        font-size: 2rem;
    }

    .hero-text h2 {
        font-size: 1.2rem;
    }

    .products-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .main-content {
        padding: 15px;
        margin-bottom: 120px;
    }

    .window-content {
        padding: 16px;
    }
}

@media (max-width: 480px) {
    .top-bar {
        padding: 0 8px;
    }

    .ubuntu-menu {
        width: calc(100vw - 20px);
        left: 10px;
        right: 10px;
        height: calc(100vh - 80px);
    }

    .app-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 12px;
    }

    .dock-container {
        flex-wrap: wrap;
        justify-content: center;
        max-width: calc(100vw - 40px);
    }

    .dock-separator {
        display: none;
    }
}
