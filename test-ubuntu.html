<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ubuntu Test</title>
    <style>
        body {
            font-family: 'Ubuntu', sans-serif;
            background: #2c001e;
            color: white;
            margin: 0;
            padding: 20px;
        }
        
        .test-window {
            width: 400px;
            height: 300px;
            background: white;
            border-radius: 8px;
            margin: 20px;
            overflow: hidden;
        }
        
        .ubuntu-header {
            height: 40px;
            background: linear-gradient(135deg, #3c3c3c, #2c2c2c);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            color: white;
        }
        
        .window-title {
            flex: 1;
            text-align: center;
            font-size: 14px;
        }
        
        .ubuntu-controls {
            display: flex;
            gap: 8px;
        }
        
        .ubuntu-control {
            width: 24px;
            height: 24px;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
            color: rgba(255, 255, 255, 0.7);
        }
        
        .ubuntu-control:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }
        
        .ubuntu-control.close:hover {
            background: rgba(220, 53, 69, 0.2);
            color: #dc3545;
        }
        
        .window-content {
            padding: 20px;
            color: black;
        }
        
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            background: #e95420;
            color: white;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <h1>Ubuntu Window Test</h1>
    
    <div class="test-window">
        <div class="ubuntu-header">
            <div class="window-title">测试窗口</div>
            <div class="ubuntu-controls">
                <div class="ubuntu-control minimize" data-action="minimize" title="最小化">
                    <svg width="16" height="16" viewBox="0 0 16 16">
                        <line x1="4" y1="8" x2="12" y2="8" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </div>
                <div class="ubuntu-control maximize" data-action="maximize" title="最大化">
                    <svg width="16" height="16" viewBox="0 0 16 16">
                        <rect x="3" y="3" width="10" height="10" fill="none" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </div>
                <div class="ubuntu-control close" data-action="close" title="关闭">
                    <svg width="16" height="16" viewBox="0 0 16 16">
                        <line x1="4" y1="4" x2="12" y2="12" stroke="currentColor" stroke-width="2"/>
                        <line x1="12" y1="4" x2="4" y2="12" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </div>
            </div>
        </div>
        <div class="window-content">
            <p>这是一个测试窗口</p>
            <button onclick="testFunction()">测试按钮</button>
        </div>
    </div>
    
    <script>
        console.log('Test page loaded');
        
        function testFunction() {
            alert('按钮工作正常！');
        }
        
        document.addEventListener('click', function(e) {
            const control = e.target.closest('.ubuntu-control');
            if (control) {
                const action = control.getAttribute('data-action');
                console.log('Control clicked:', action);
                
                switch(action) {
                    case 'close':
                        alert('关闭按钮被点击');
                        break;
                    case 'minimize':
                        alert('最小化按钮被点击');
                        break;
                    case 'maximize':
                        alert('最大化按钮被点击');
                        break;
                }
            }
        });
    </script>
</body>
</html>
