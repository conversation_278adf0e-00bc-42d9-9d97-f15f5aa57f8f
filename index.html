<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>北京卿逸科技中心 - 创新科技，千年传承</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="logo">
                <svg width="32" height="32" viewBox="0 0 32 32" class="logo-icon">
                    <defs>
                        <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#0066FF;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#4285F4;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <!-- 简洁的几何图形 -->
                    <rect x="6" y="6" width="20" height="20" rx="4" fill="none" stroke="url(#logoGradient)" stroke-width="2"/>
                    <circle cx="16" cy="16" r="6" fill="url(#logoGradient)" opacity="0.2"/>
                    <circle cx="16" cy="16" r="3" fill="url(#logoGradient)"/>
                    <!-- 科技感的小点 -->
                    <circle cx="16" cy="8" r="1.5" fill="var(--accent-color)"/>
                    <circle cx="24" cy="16" r="1.5" fill="var(--accent-color)"/>
                    <circle cx="16" cy="24" r="1.5" fill="var(--accent-color)"/>
                    <circle cx="8" cy="16" r="1.5" fill="var(--accent-color)"/>
                </svg>
                <span class="logo-text">卿逸科技</span>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <button class="nav-link" onclick="currentSlide(1)">
                        <span class="nav-text">首页</span>
                    </button>
                </li>
                <li class="nav-item">
                    <button class="nav-link" onclick="currentSlide(2)">
                        <span class="nav-text">产品</span>
                    </button>
                </li>
                <li class="nav-item">
                    <button class="nav-link" onclick="currentSlide(3)">
                        <span class="nav-text">方案</span>
                    </button>
                </li>
                <li class="nav-item">
                    <button class="nav-link" onclick="currentSlide(4)">
                        <span class="nav-text">关于</span>
                    </button>
                </li>
                <li class="nav-item">
                    <a href="#footer" class="nav-link">
                        <span class="nav-text">联系</span>
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Slideshow Container -->
    <div class="slideshow-container">
        <!-- Slide 1: 首页/品牌介绍 -->
        <div class="slide active" id="slide-home">
            <div class="slide-content">
                <div class="slide-text">
                    <h1 class="slide-title">创新科技<br><span class="gradient-text">千年传承</span></h1>
                    <p class="slide-subtitle">北京卿逸科技中心致力于将传统智慧与现代科技完美融合，为企业数字化转型提供前沿解决方案</p>
                    <div class="slide-buttons">
                        <button class="btn-primary" onclick="nextSlide()">了解更多</button>
                        <button class="btn-secondary">观看演示</button>
                    </div>
                </div>
                <div class="slide-visual">
                    <div class="floating-elements">
                        <div class="element element-1"></div>
                        <div class="element element-2"></div>
                        <div class="element element-3"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 2: 核心产品 -->
        <div class="slide" id="slide-products">
            <div class="slide-content">
                <div class="slide-header">
                    <h2 class="slide-title">核心产品矩阵</h2>
                    <p class="slide-subtitle">构建企业数字化转型的技术基石，赋能未来商业创新</p>
                </div>
                <div class="products-showcase">
                    <div class="product-card-large">
                        <div class="product-header">
                            <div class="product-icon-large">🚀</div>
                            <div class="product-title-section">
                                <h3>智能云平台</h3>
                                <p class="product-tagline">AI驱动的企业级云计算解决方案</p>
                            </div>
                        </div>
                        <div class="product-description">
                            <p>基于人工智能和云原生技术构建的企业级云计算平台，提供弹性扩展、智能运维、安全可靠的云服务体验，助力企业实现数字化转型升级。</p>
                        </div>
                        <div class="product-features">
                            <div class="feature-item">
                                <span class="feature-number">99.9%</span>
                                <span class="feature-label">服务可用性</span>
                            </div>
                            <div class="feature-item">
                                <span class="feature-number">< 10ms</span>
                                <span class="feature-label">响应时间</span>
                            </div>
                            <div class="feature-item">
                                <span class="feature-number">1000+</span>
                                <span class="feature-label">企业客户</span>
                            </div>
                        </div>
                    </div>
                    <div class="product-card-large">
                        <div class="product-header">
                            <div class="product-icon-large">🔮</div>
                            <div class="product-title-section">
                                <h3>数据分析引擎</h3>
                                <p class="product-tagline">实时大数据处理与智能分析平台</p>
                            </div>
                        </div>
                        <div class="product-description">
                            <p>集成机器学习算法的大数据分析平台，支持实时数据处理、可视化分析和预测建模，为企业决策提供数据驱动的智能洞察。</p>
                        </div>
                        <div class="product-features">
                            <div class="feature-item">
                                <span class="feature-number">PB级</span>
                                <span class="feature-label">数据处理</span>
                            </div>
                            <div class="feature-item">
                                <span class="feature-number">实时</span>
                                <span class="feature-label">分析能力</span>
                            </div>
                            <div class="feature-item">
                                <span class="feature-number">50+</span>
                                <span class="feature-label">算法模型</span>
                            </div>
                        </div>
                    </div>
                    <div class="product-card-large">
                        <div class="product-header">
                            <div class="product-icon-large">⚡</div>
                            <div class="product-title-section">
                                <h3>智能自动化平台</h3>
                                <p class="product-tagline">端到端业务流程自动化解决方案</p>
                            </div>
                        </div>
                        <div class="product-description">
                            <p>基于RPA和AI技术的智能自动化平台，实现业务流程的端到端自动化，提升运营效率，降低人工成本，释放员工创造力。</p>
                        </div>
                        <div class="product-features">
                            <div class="feature-item">
                                <span class="feature-number">80%</span>
                                <span class="feature-label">效率提升</span>
                            </div>
                            <div class="feature-item">
                                <span class="feature-number">零代码</span>
                                <span class="feature-label">快速配置</span>
                            </div>
                            <div class="feature-item">
                                <span class="feature-number">24/7</span>
                                <span class="feature-label">无人值守</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 3: 解决方案 -->
        <div class="slide" id="slide-solutions">
            <div class="slide-content">
                <div class="slide-header">
                    <h2 class="slide-title">行业解决方案</h2>
                    <p class="slide-subtitle">深度融合行业场景，打造定制化数字转型路径</p>
                </div>
                <div class="solutions-grid-large">
                    <div class="solution-card-large">
                        <div class="solution-visual">
                            <div class="solution-icon-large">🏢</div>
                            <div class="solution-bg-pattern"></div>
                        </div>
                        <div class="solution-content">
                            <h3>企业数字化转型</h3>
                            <p class="solution-description">
                                为传统企业提供全方位数字化转型咨询与实施服务，从战略规划到技术落地，
                                助力企业在数字时代重塑竞争优势，实现可持续发展。
                            </p>
                            <div class="solution-highlights">
                                <div class="highlight-item">
                                    <h4>业务流程重塑</h4>
                                    <p>数字化业务流程，提升运营效率</p>
                                </div>
                                <div class="highlight-item">
                                    <h4>数据驱动决策</h4>
                                    <p>构建数据中台，实现智能决策</p>
                                </div>
                                <div class="highlight-item">
                                    <h4>组织能力升级</h4>
                                    <p>培养数字化人才，建设敏捷组织</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="solution-card-large">
                        <div class="solution-visual">
                            <div class="solution-icon-large">☁️</div>
                            <div class="solution-bg-pattern"></div>
                        </div>
                        <div class="solution-content">
                            <h3>云原生架构升级</h3>
                            <p class="solution-description">
                                基于云原生技术栈，为企业构建可扩展、高可用、安全可靠的现代化应用架构，
                                实现敏捷开发、持续交付和弹性运维。
                            </p>
                            <div class="solution-highlights">
                                <div class="highlight-item">
                                    <h4>微服务架构</h4>
                                    <p>模块化设计，提升系统灵活性</p>
                                </div>
                                <div class="highlight-item">
                                    <h4>容器化部署</h4>
                                    <p>标准化交付，简化运维管理</p>
                                </div>
                                <div class="highlight-item">
                                    <h4>DevOps 实践</h4>
                                    <p>自动化流水线，加速产品迭代</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="solution-card-large">
                        <div class="solution-visual">
                            <div class="solution-icon-large">🤖</div>
                            <div class="solution-bg-pattern"></div>
                        </div>
                        <div class="solution-content">
                            <h3>AI 智能化应用</h3>
                            <p class="solution-description">
                                将前沿人工智能技术深度融入业务场景，构建智能化应用生态，
                                提升企业智能化水平和运营效率，创造新的商业价值。
                            </p>
                            <div class="solution-highlights">
                                <div class="highlight-item">
                                    <h4>智能客服系统</h4>
                                    <p>7×24小时智能服务，提升客户体验</p>
                                </div>
                                <div class="highlight-item">
                                    <h4>预测性分析</h4>
                                    <p>机器学习算法，预测业务趋势</p>
                                </div>
                                <div class="highlight-item">
                                    <h4>智能决策引擎</h4>
                                    <p>AI辅助决策，优化业务流程</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 4: 关于我们 -->
        <div class="slide" id="slide-about">
            <div class="slide-content">
                <div class="about-hero">
                    <div class="about-main">
                        <h2 class="slide-title">卿逸科技</h2>
                        <p class="about-tagline">创新科技 · 千年传承</p>
                        <div class="about-description-large">
                            <p>
                                北京卿逸科技中心成立于2024年，是一家专注于企业数字化转型的创新科技公司。
                                我们秉承"创新科技，千年传承"的理念，致力于将前沿技术与传统智慧相结合，
                                为客户提供卓越的技术解决方案，助力企业在数字化浪潮中乘风破浪。
                            </p>
                        </div>
                    </div>
                    <div class="about-visual-large">
                        <div class="company-metrics">
                            <div class="metric-circle">
                                <div class="metric-content">
                                    <span class="metric-number">2024</span>
                                    <span class="metric-label">成立年份</span>
                                </div>
                            </div>
                            <div class="metric-circle">
                                <div class="metric-content">
                                    <span class="metric-number">50+</span>
                                    <span class="metric-label">成功项目</span>
                                </div>
                            </div>
                            <div class="metric-circle">
                                <div class="metric-content">
                                    <span class="metric-number">20+</span>
                                    <span class="metric-label">合作伙伴</span>
                                </div>
                            </div>
                            <div class="metric-circle">
                                <div class="metric-content">
                                    <span class="metric-number">99%</span>
                                    <span class="metric-label">客户满意度</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="company-values">
                    <div class="value-item">
                        <div class="value-icon">🎯</div>
                        <h3>使命愿景</h3>
                        <p>成为企业数字化转型的首选合作伙伴，用科技创新推动商业进步</p>
                    </div>
                    <div class="value-item">
                        <div class="value-icon">💡</div>
                        <h3>核心价值</h3>
                        <p>客户至上、创新驱动、品质卓越、合作共赢</p>
                    </div>
                    <div class="value-item">
                        <div class="value-icon">🚀</div>
                        <h3>发展理念</h3>
                        <p>传承千年智慧，融合现代科技，创造无限可能</p>
                    </div>
                </div>
            </div>
        </div>


    </div>

    <!-- Footer -->
    <footer id="footer" class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-info">
                    <div class="logo">
                        <svg width="32" height="32" viewBox="0 0 40 40">
                            <circle cx="20" cy="20" r="18" fill="none" stroke="currentColor" stroke-width="2"/>
                            <path d="M12 20 L20 12 L28 20 L20 28 Z" fill="currentColor"/>
                            <circle cx="20" cy="20" r="3" fill="white"/>
                        </svg>
                        <span>北京卿逸科技中心</span>
                    </div>
                    <p>创新科技，千年传承</p>
                </div>
                <div class="footer-links">
                    <div class="link-group">
                        <h4>产品</h4>
                        <a href="#">智能云平台</a>
                        <a href="#">数据分析</a>
                        <a href="#">自动化平台</a>
                    </div>
                    <div class="link-group">
                        <h4>服务</h4>
                        <a href="#">技术支持</a>
                        <a href="#">咨询服务</a>
                        <a href="#">培训认证</a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 北京卿逸科技中心. 保留所有权利.</p>
                <p>备案号：<a href="https://beian.miit.gov.cn/" target="_blank">京ICP备2025107618号-1</a></p>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>