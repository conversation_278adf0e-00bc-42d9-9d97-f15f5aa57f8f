<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>北京卿逸科技中心 - 创新科技，千年传承</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="logo">
                <svg width="32" height="32" viewBox="0 0 32 32" class="logo-icon">
                    <defs>
                        <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#0066FF;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#4285F4;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <!-- 简洁的几何图形 -->
                    <rect x="6" y="6" width="20" height="20" rx="4" fill="none" stroke="url(#logoGradient)" stroke-width="2"/>
                    <circle cx="16" cy="16" r="6" fill="url(#logoGradient)" opacity="0.2"/>
                    <circle cx="16" cy="16" r="3" fill="url(#logoGradient)"/>
                    <!-- 科技感的小点 -->
                    <circle cx="16" cy="8" r="1.5" fill="var(--accent-color)"/>
                    <circle cx="24" cy="16" r="1.5" fill="var(--accent-color)"/>
                    <circle cx="16" cy="24" r="1.5" fill="var(--accent-color)"/>
                    <circle cx="8" cy="16" r="1.5" fill="var(--accent-color)"/>
                </svg>
                <span class="logo-text">卿逸科技</span>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="#home" class="nav-link">
                        <span class="nav-text">首页</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#products" class="nav-link">
                        <span class="nav-text">产品</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#solutions" class="nav-link">
                        <span class="nav-text">解决方案</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#about" class="nav-link">
                        <span class="nav-text">关于我们</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#contact" class="nav-link">
                        <span class="nav-text">联系我们</span>
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-content">
            <h1 class="hero-title">创新科技<br><span class="gradient-text">千年传承</span></h1>
            <p class="hero-subtitle">北京卿逸科技中心致力于将传统智慧与现代科技完美融合，<br>为企业数字化转型提供前沿解决方案</p>
            <div class="hero-buttons">
                <button class="btn-primary">了解更多</button>
                <button class="btn-secondary">观看演示</button>
            </div>
        </div>
        <div class="hero-visual">
            <div class="floating-elements">
                <div class="element element-1"></div>
                <div class="element element-2"></div>
                <div class="element element-3"></div>
            </div>
        </div>
    </section>

    <!-- Products Section -->
    <section id="products" class="products">
        <div class="container">
            <h2 class="section-title">核心产品</h2>
            <div class="products-grid">
                <div class="product-card">
                    <div class="product-icon">🚀</div>
                    <h3>智能云平台</h3>
                    <p>基于AI的企业级云计算解决方案，提供弹性扩展和智能运维</p>
                    <div class="tech-stats">
                        <span class="stat-item">99.9% 可用性</span>
                        <span class="stat-item">毫秒级响应</span>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-icon">🔮</div>
                    <h3>数据分析引擎</h3>
                    <p>实时大数据处理与可视化分析，助力企业决策智能化</p>
                    <div class="tech-stats">
                        <span class="stat-item">PB级处理</span>
                        <span class="stat-item">实时分析</span>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-icon">⚡</div>
                    <h3>自动化平台</h3>
                    <p>端到端业务流程自动化，提升企业运营效率</p>
                    <div class="tech-stats">
                        <span class="stat-item">80% 效率提升</span>
                        <span class="stat-item">零代码配置</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-info">
                    <div class="logo">
                        <svg width="32" height="32" viewBox="0 0 40 40">
                            <circle cx="20" cy="20" r="18" fill="none" stroke="currentColor" stroke-width="2"/>
                            <path d="M12 20 L20 12 L28 20 L20 28 Z" fill="currentColor"/>
                            <circle cx="20" cy="20" r="3" fill="white"/>
                        </svg>
                        <span>北京卿逸科技中心</span>
                    </div>
                    <p>创新科技，千年传承</p>
                </div>
                <div class="footer-links">
                    <div class="link-group">
                        <h4>产品</h4>
                        <a href="#">智能云平台</a>
                        <a href="#">数据分析</a>
                        <a href="#">自动化平台</a>
                    </div>
                    <div class="link-group">
                        <h4>服务</h4>
                        <a href="#">技术支持</a>
                        <a href="#">咨询服务</a>
                        <a href="#">培训认证</a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 北京卿逸科技中心. 保留所有权利.</p>
                <p>备案号：<a href="https://beian.miit.gov.cn/" target="_blank">京ICP备2025107618号-1</a></p>
                <p>官网：<a href="https://www.1000years.net">www.1000years.net</a></p>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>