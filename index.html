<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>卿逸科技 - Ubuntu风格</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Ubuntu:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Ubuntu Style Desktop -->
    <div class="ubuntu-desktop">
        <!-- Top Bar -->
        <div class="top-bar">
            <div class="top-bar-left">
                <div class="task-manager-button" id="task-manager-btn">
                    <div class="task-icon">
                        <div class="task-icon-bar"></div>
                        <div class="task-icon-bar"></div>
                        <div class="task-icon-bar"></div>
                    </div>
                    <span>任务管理</span>
                </div>
            </div>
            <div class="top-bar-center">
                <div class="clock">7月16日 20:47</div>
            </div>
            <div class="top-bar-right">
                <div class="system-indicators">
                    <a href="https://beian.miit.gov.cn/" target="_blank" class="beian-link" title="工信部备案查询">
                        京ICP备2025107618号-1
                    </a>
                    <div class="theme-switcher" id="theme-switcher" title="切换主题">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                            <circle cx="12" cy="12" r="4" stroke="currentColor" stroke-width="2"/>
                            <path d="M12 2V4" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                            <path d="M12 20V22" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                            <path d="M4.93 4.93L6.34 6.34" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                            <path d="M17.66 17.66L19.07 19.07" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                            <path d="M2 12H4" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                            <path d="M20 12H22" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                            <path d="M6.34 17.66L4.93 19.07" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                            <path d="M19.07 4.93L17.66 6.34" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                        </svg>
                    </div>
                    <div class="indicator" id="music-player" title="音乐播放器">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                            <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                            <path d="M12 1V6" stroke="currentColor" stroke-width="2"/>
                            <path d="M12 18V23" stroke="currentColor" stroke-width="2"/>
                            <path d="M4.22 4.22L7.76 7.76" stroke="currentColor" stroke-width="2"/>
                            <path d="M16.24 16.24L19.78 19.78" stroke="currentColor" stroke-width="2"/>
                            <path d="M1 12H6" stroke="currentColor" stroke-width="2"/>
                            <path d="M18 12H23" stroke="currentColor" stroke-width="2"/>
                            <path d="M4.22 19.78L7.76 16.24" stroke="currentColor" stroke-width="2"/>
                            <path d="M16.24 7.76L19.78 4.22" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                    <div class="indicator" id="bookmarks" title="书签">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                            <path d="M19 21L12 16L5 21V5C5 4.46957 5.21071 3.96086 5.58579 3.58579C5.96086 3.21071 6.46957 3 7 3H17C17.5304 3 18.0391 3.21071 18.4142 3.58579C18.7893 3.96086 19 4.46957 19 5V21Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <div class="indicator" id="wechat-qr" title="微信联系">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                            <path d="M21 15C21 15.5304 20.7893 16.0391 20.4142 16.4142C20.0391 16.7893 19.5304 17 19 17H7L3 21V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V15Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M14 9H14.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M10 9H10.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M6 9H6.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <div class="indicator" id="settings" title="系统设置">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                            <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                            <path d="M19.4 15C19.2669 15.3016 19.2272 15.6362 19.286 15.9606C19.3448 16.285 19.4995 16.5843 19.73 16.82L19.79 16.88C19.976 17.0657 20.1235 17.2863 20.2241 17.5291C20.3248 17.7719 20.3766 18.0322 20.3766 18.295C20.3766 18.5578 20.3248 18.8181 20.2241 19.0609C20.1235 19.3037 19.976 19.5243 19.79 19.71C19.6043 19.896 19.3837 20.0435 19.1409 20.1441C18.8981 20.2448 18.6378 20.2966 18.375 20.2966C18.1122 20.2966 17.8519 20.2448 17.6091 20.1441C17.3663 20.0435 17.1457 19.896 16.96 19.71L16.9 19.65C16.6643 19.4195 16.365 19.2648 16.0406 19.206C15.7162 19.1472 15.3816 19.1869 15.08 19.32C14.7842 19.4468 14.532 19.6572 14.3543 19.9255C14.1766 20.1938 14.0813 20.5082 14.08 20.83V21C14.08 21.5304 13.8693 22.0391 13.4942 22.4142C13.1191 22.7893 12.6104 23 12.08 23C11.5496 23 11.0409 22.7893 10.6658 22.4142C10.2907 22.0391 10.08 21.5304 10.08 21V20.91C10.0723 20.579 9.96512 20.2573 9.77251 19.9887C9.5799 19.7201 9.31074 19.5176 9 19.41C8.69838 19.2769 8.36381 19.2372 8.03941 19.296C7.71502 19.3548 7.41568 19.5095 7.18 19.74L7.12 19.8C6.93425 19.986 6.71368 20.1335 6.47088 20.2341C6.22808 20.3348 5.96783 20.3866 5.705 20.3866C5.44217 20.3866 5.18192 20.3348 4.93912 20.2341C4.69632 20.1335 4.47575 19.986 4.29 19.8C4.10405 19.6143 3.95653 19.3937 3.85588 19.1509C3.75523 18.9081 3.70343 18.6478 3.70343 18.385C3.70343 18.1222 3.75523 17.8619 3.85588 17.6191C3.95653 17.3763 4.10405 17.1557 4.29 16.97L4.35 16.91C4.58054 16.6743 4.73519 16.375 4.794 16.0506C4.85282 15.7262 4.81312 15.3916 4.68 15.09C4.55324 14.7942 4.34276 14.542 4.07447 14.3643C3.80618 14.1866 3.49179 14.0913 3.17 14.09H3C2.46957 14.09 1.96086 13.8793 1.58579 13.5042C1.21071 13.1291 1 12.6204 1 12.09C1 11.5596 1.21071 11.0509 1.58579 10.6758C1.96086 10.3007 2.46957 10.09 3 10.09H3.09C3.42099 10.0823 3.742 9.97512 4.01062 9.78251C4.27925 9.5899 4.48167 9.32074 4.59 9.01C4.72312 8.70838 4.76282 8.37381 4.704 8.04941C4.64519 7.72502 4.49054 7.42568 4.26 7.19L4.2 7.13C4.01405 6.94425 3.86653 6.72368 3.76588 6.48088C3.66523 6.23808 3.61343 5.97783 3.61343 5.715C3.61343 5.45217 3.66523 5.19192 3.76588 4.94912C3.86653 4.70632 4.01405 4.48575 4.2 4.3C4.38575 4.11405 4.60632 3.96653 4.84912 3.86588C5.09192 3.76523 5.35217 3.71343 5.615 3.71343C5.87783 3.71343 6.13808 3.76523 6.38088 3.86588C6.62368 3.96653 6.84425 4.11405 7.03 4.3L7.09 4.36C7.32568 4.59054 7.62502 4.74519 7.94941 4.804C8.27381 4.86282 8.60838 4.82312 8.91 4.69H9C9.29577 4.56324 9.54802 4.35276 9.72569 4.08447C9.90337 3.81618 9.99872 3.50179 10 3.18V3C10 2.46957 10.2107 1.96086 10.5858 1.58579C10.9609 1.21071 11.4696 1 12 1C12.5304 1 13.0391 1.21071 13.4142 1.58579C13.7893 1.96086 14 2.46957 14 3V3.09C14.0013 3.41179 14.0966 3.72618 14.2743 3.99447C14.452 4.26276 14.7042 4.47324 15 4.6C15.3016 4.73312 15.6362 4.77282 15.9606 4.714C16.285 4.65519 16.5843 4.50054 16.82 4.27L16.88 4.21C17.0657 4.02405 17.2863 3.87653 17.5291 3.77588C17.7719 3.67523 18.0322 3.62343 18.295 3.62343C18.5578 3.62343 18.8181 3.67523 19.0609 3.77588C19.3037 3.87653 19.5243 4.02405 19.71 4.21C19.896 4.39575 20.0435 4.61632 20.1441 4.85912C20.2448 5.10192 20.2966 5.36217 20.2966 5.625C20.2966 5.88783 20.2448 6.14808 20.1441 6.39088C20.0435 6.63368 19.896 6.85425 19.71 7.04L19.65 7.1C19.4195 7.33568 19.2648 7.63502 19.206 7.95941C19.1472 8.28381 19.1869 8.61838 19.32 8.92V9C19.4468 9.29577 19.6572 9.54802 19.9255 9.72569C20.1938 9.90337 20.5082 9.99872 20.83 10H21C21.5304 10 22.0391 10.2107 22.4142 10.5858C22.7893 10.9609 23 11.4696 23 12C23 12.5304 22.7893 13.0391 22.4142 13.4142C22.0391 13.7893 21.5304 14 21 14H20.91C20.5882 14.0013 20.2738 14.0966 20.0055 14.2743C19.7372 14.452 19.5268 14.7042 19.4 15Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="desktop-content">
            <!-- Main Content - Desktop Background -->
            <div class="main-content">
                <!-- Desktop Background -->
                <div class="desktop-background">
                    <div class="desktop-wallpaper">
                        <div class="welcome-text">
                            <h1>欢迎使用卿逸科技系统</h1>
                            <p>点击应用菜单或底部Dock开始探索</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Mac Style Dock -->
            <div class="mac-dock">
                <div class="dock-container">
                    <div class="dock-item active" data-view="home">
                        <div class="dock-icon">
                            <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
                                <path d="M3 12L5 10M5 10L12 3L19 10M5 10V20C5 20.5523 5.44772 21 6 21H9M19 10L21 12M19 10V20C19 20.5523 18.5523 21 18 21H15M9 21C9.55228 21 10 20.5523 10 20V16C10 15.4477 10.4477 15 11 15H13C13.5523 15 14 15.4477 14 16V20C14 20.5523 14.4477 21 15 21M9 21H15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="dock-label">首页</div>
                    </div>
                    <div class="dock-item" data-view="products">
                        <div class="dock-icon">
                            <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
                                <path d="M20 7L12 3L4 7M20 7L12 11M20 7V17L12 21M12 11L4 7M12 11V21M4 7V17L12 21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M16 5L8 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="dock-label">产品</div>
                    </div>
                    <div class="dock-item" data-view="solutions">
                        <div class="dock-icon">
                            <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
                                <path d="M14.7 6.3C15.1 5.9 15.1 5.3 14.7 4.9C14.3 4.5 13.7 4.5 13.3 4.9L9 9.2C8.6 9.6 8.6 10.2 9 10.6L13.3 14.9C13.7 15.3 14.3 15.3 14.7 14.9C15.1 14.5 15.1 13.9 14.7 13.5L11.4 10.2L14.7 6.9V6.3Z" fill="currentColor"/>
                                <path d="M9.3 6.3C8.9 5.9 8.9 5.3 9.3 4.9C9.7 4.5 10.3 4.5 10.7 4.9L15 9.2C15.4 9.6 15.4 10.2 15 10.6L10.7 14.9C10.3 15.3 9.7 15.3 9.3 14.9C8.9 14.5 8.9 13.9 9.3 13.5L12.6 10.2L9.3 6.9V6.3Z" fill="currentColor"/>
                                <circle cx="12" cy="12" r="9" stroke="currentColor" stroke-width="2"/>
                                <path d="M8 12L16 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                <path d="M12 8L12 16" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                            </svg>
                        </div>
                        <div class="dock-label">方案</div>
                    </div>
                    <div class="dock-item" data-view="about">
                        <div class="dock-icon">
                            <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
                                <circle cx="12" cy="12" r="9" stroke="currentColor" stroke-width="2"/>
                                <path d="M12 8V12L15 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <circle cx="12" cy="6" r="1" fill="currentColor"/>
                            </svg>
                        </div>
                        <div class="dock-label">关于</div>
                    </div>
                    <div class="dock-item" data-view="contact">
                        <div class="dock-icon">
                            <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
                                <path d="M22 12C22 17.5228 17.5228 22 12 22C10.1786 22 8.47087 21.4135 7.09899 20.4279L2 22L3.57212 16.901C2.58651 15.5291 2 13.8214 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M8 12H8.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M12 12H12.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M16 12H16.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="dock-label">联系</div>
                    </div>

                    <div class="dock-separator"></div>

                    <div class="dock-item" data-app="analytics">
                        <div class="dock-icon">
                            <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
                                <path d="M3 3V21H21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M7 16L12 11L16 15L21 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <circle cx="7" cy="16" r="1" fill="currentColor"/>
                                <circle cx="12" cy="11" r="1" fill="currentColor"/>
                                <circle cx="16" cy="15" r="1" fill="currentColor"/>
                                <circle cx="21" cy="10" r="1" fill="currentColor"/>
                            </svg>
                        </div>
                        <div class="dock-label">分析</div>
                    </div>
                    <div class="dock-item" data-app="cloud">
                        <div class="dock-icon">
                            <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
                                <path d="M18 10H20C21.1046 10 22 10.8954 22 12C22 13.1046 21.1046 14 20 14H18M6 14H4C2.89543 14 2 13.1046 2 12C2 10.8954 2.89543 10 4 10H6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M8 12C8 8.68629 10.6863 6 14 6C17.3137 6 20 8.68629 20 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M8 16C8 17.1046 8.89543 18 10 18H18C19.1046 18 20 17.1046 20 16" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="dock-label">云端</div>
                    </div>
                    <div class="dock-item" data-app="ai">
                        <div class="dock-icon">
                            <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
                                <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                                <path d="M12 1V3" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                <path d="M12 21V23" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                <path d="M4.22 4.22L5.64 5.64" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                <path d="M18.36 18.36L19.78 19.78" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                <path d="M1 12H3" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                <path d="M21 12H23" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                <path d="M4.22 19.78L5.64 18.36" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                <path d="M18.36 5.64L19.78 4.22" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                            </svg>
                        </div>
                        <div class="dock-label">AI</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Window Applications -->
    <!-- Home Window -->
    <div class="app-window" id="home-window" data-app="home">
        <div class="window-header ubuntu-header">
            <div class="window-title">卿逸科技 - 创新科技，千年传承</div>
            <div class="window-controls ubuntu-controls">
                <div class="ubuntu-control minimize" data-action="minimize" title="最小化" onclick="window.windowMinimize('home')">−</div>
                <div class="ubuntu-control maximize" data-action="maximize" title="最大化" onclick="window.windowMaximize('home')">□</div>
                <div class="ubuntu-control close" data-action="close" title="关闭" onclick="window.windowClose('home')">×</div>
            </div>
        </div>
        
        <div class="window-content">
            <div class="hero-section">
                <div class="hero-text">
                    <h1>北京卿逸科技中心</h1>
                    <h2>创新科技 · 千年传承</h2>
                    <p>专注于企业数字化转型的创新科技公司，致力于将前沿技术与传统智慧相结合</p>
                    <div class="action-buttons">
                        <button class="ubuntu-btn primary" data-open="products">了解产品</button>
                        <button class="ubuntu-btn secondary" data-open="about">关于我们</button>
                    </div>
                </div>
                <div class="hero-visual">
                    <div class="ubuntu-logo-large">
                        <div class="logo-ring"></div>
                        <div class="logo-center">卿逸</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Products Window -->
    <div class="app-window" id="products-window" data-app="products">
        <div class="window-header ubuntu-header">
            <div class="window-title">核心产品</div>
            <div class="window-controls ubuntu-controls">
                <div class="ubuntu-control minimize" data-action="minimize" title="最小化" onclick="window.windowMinimize('products')">−</div>
                <div class="ubuntu-control maximize" data-action="maximize" title="最大化" onclick="window.windowMaximize('products')">□</div>
                <div class="ubuntu-control close" data-action="close" title="关闭" onclick="window.windowClose('products')">×</div>
            </div>
        </div>
        
        <div class="window-content">
            <div class="products-grid">
                <div class="product-card ubuntu-card">
                    <div class="card-header">
                        <div class="product-icon">🚀</div>
                        <h3>智能云平台</h3>
                    </div>
                    <div class="card-content">
                        <p>基于AI的企业级云计算解决方案，提供弹性扩展和智能运维</p>
                        <div class="features">
                            <span class="feature-tag">99.9% 可用性</span>
                            <span class="feature-tag">毫秒级响应</span>
                        </div>
                    </div>
                </div>
                
                <div class="product-card ubuntu-card">
                    <div class="card-header">
                        <div class="product-icon">🔮</div>
                        <h3>数据分析引擎</h3>
                    </div>
                    <div class="card-content">
                        <p>实时大数据处理与可视化分析，助力企业决策智能化</p>
                        <div class="features">
                            <span class="feature-tag">PB级处理</span>
                            <span class="feature-tag">实时分析</span>
                        </div>
                    </div>
                </div>
                
                <div class="product-card ubuntu-card">
                    <div class="card-header">
                        <div class="product-icon">⚡</div>
                        <h3>自动化平台</h3>
                    </div>
                    <div class="card-content">
                        <p>端到端业务流程自动化，提升企业运营效率</p>
                        <div class="features">
                            <span class="feature-tag">80% 效率提升</span>
                            <span class="feature-tag">零代码配置</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Solutions Window -->
    <div class="app-window" id="solutions-window" data-app="solutions">
        <div class="window-header ubuntu-header">
            <div class="window-title">解决方案</div>
            <div class="window-controls ubuntu-controls">
                <div class="ubuntu-control minimize" data-action="minimize" title="最小化" onclick="window.windowMinimize('solutions')">−</div>
                <div class="ubuntu-control maximize" data-action="maximize" title="最大化" onclick="window.windowMaximize('solutions')">□</div>
                <div class="ubuntu-control close" data-action="close" title="关闭" onclick="window.windowClose('solutions')">×</div>
            </div>
        </div>

        <div class="window-content">
            <div class="solutions-list">
                <div class="solution-item ubuntu-card">
                    <div class="solution-header">
                        <div class="solution-icon">🏢</div>
                        <div class="solution-info">
                            <h3>企业数字化转型</h3>
                            <p>为传统企业提供全方位数字化转型咨询与实施服务</p>
                        </div>
                    </div>
                    <div class="solution-details">
                        <ul>
                            <li>业务流程数字化</li>
                            <li>数据驱动决策</li>
                            <li>智能运营管理</li>
                        </ul>
                    </div>
                </div>

                <div class="solution-item ubuntu-card">
                    <div class="solution-header">
                        <div class="solution-icon">☁️</div>
                        <div class="solution-info">
                            <h3>云原生架构</h3>
                            <p>基于云原生技术栈，构建可扩展、高可用的现代化应用架构</p>
                        </div>
                    </div>
                    <div class="solution-details">
                        <ul>
                            <li>微服务架构设计</li>
                            <li>容器化部署</li>
                            <li>DevOps 自动化</li>
                        </ul>
                    </div>
                </div>

                <div class="solution-item ubuntu-card">
                    <div class="solution-header">
                        <div class="solution-icon">🤖</div>
                        <div class="solution-info">
                            <h3>AI 智能应用</h3>
                            <p>将人工智能技术融入业务场景，提升企业智能化水平</p>
                        </div>
                    </div>
                    <div class="solution-details">
                        <ul>
                            <li>智能客服系统</li>
                            <li>预测性分析</li>
                            <li>自动化决策</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- About Window -->
    <div class="app-window" id="about-window" data-app="about">
        <div class="window-header ubuntu-header">
            <div class="window-title">关于我们</div>
            <div class="window-controls ubuntu-controls">
                <div class="ubuntu-control minimize" data-action="minimize" title="最小化" onclick="window.windowMinimize('about')">−</div>
                <div class="ubuntu-control maximize" data-action="maximize" title="最大化" onclick="window.windowMaximize('about')">□</div>
                <div class="ubuntu-control close" data-action="close" title="关闭" onclick="window.windowClose('about')">×</div>
            </div>
        </div>

        <div class="window-content">
            <div class="about-content">
                <div class="company-info ubuntu-card">
                    <h3>公司简介</h3>
                    <p>北京卿逸科技中心成立于2024年，是一家专注于企业数字化转型的创新科技公司。我们秉承"创新科技，千年传承"的理念，致力于将前沿技术与传统智慧相结合。</p>

                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-number">50+</div>
                            <div class="stat-label">成功项目</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">20+</div>
                            <div class="stat-label">合作伙伴</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">99%</div>
                            <div class="stat-label">客户满意度</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Contact Window -->
    <div class="app-window" id="contact-window" data-app="contact">
        <div class="window-header ubuntu-header">
            <div class="window-title">联系我们</div>
            <div class="window-controls ubuntu-controls">
                <div class="ubuntu-control minimize" data-action="minimize" title="最小化" onclick="window.windowMinimize('contact')">−</div>
                <div class="ubuntu-control maximize" data-action="maximize" title="最大化" onclick="window.windowMaximize('contact')">□</div>
                <div class="ubuntu-control close" data-action="close" title="关闭" onclick="window.windowClose('contact')">×</div>
            </div>
        </div>

        <div class="window-content">
            <div class="contact-info ubuntu-card">
                <h3>联系方式</h3>
                <div class="contact-details">
                    <div class="contact-item">
                        <strong>公司名称：</strong>北京卿逸科技中心
                    </div>
                    <div class="contact-item">
                        <strong>备案号：</strong>京ICP备2025107618号-1
                    </div>
                    <div class="contact-item">
                        <strong>成立时间：</strong>2024年
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Theme Selector Panel -->
    <div class="theme-panel" id="theme-panel">
        <div class="theme-panel-header">
            <h3>选择主题</h3>
            <button class="theme-panel-close" id="theme-panel-close">×</button>
        </div>
        <div class="theme-options">
            <div class="theme-option active" data-theme="ubuntu-default">
                <div class="theme-preview">
                    <div class="theme-bg ubuntu-default-bg"></div>
                    <div class="theme-dock ubuntu-default-dock"></div>
                    <div class="theme-window ubuntu-default-window"></div>
                </div>
                <span class="theme-name">Ubuntu 经典</span>
            </div>
            <div class="theme-option" data-theme="ubuntu-dark">
                <div class="theme-preview">
                    <div class="theme-bg ubuntu-dark-bg"></div>
                    <div class="theme-dock ubuntu-dark-dock"></div>
                    <div class="theme-window ubuntu-dark-window"></div>
                </div>
                <span class="theme-name">Ubuntu 深色</span>
            </div>
            <div class="theme-option" data-theme="ubuntu-light">
                <div class="theme-preview">
                    <div class="theme-bg ubuntu-light-bg"></div>
                    <div class="theme-dock ubuntu-light-dock"></div>
                    <div class="theme-window ubuntu-light-window"></div>
                </div>
                <span class="theme-name">Ubuntu 浅色</span>
            </div>
            <div class="theme-option" data-theme="ubuntu-purple">
                <div class="theme-preview">
                    <div class="theme-bg ubuntu-purple-bg"></div>
                    <div class="theme-dock ubuntu-purple-dock"></div>
                    <div class="theme-window ubuntu-purple-window"></div>
                </div>
                <span class="theme-name">Ubuntu 紫色</span>
            </div>
            <div class="theme-option" data-theme="ubuntu-green">
                <div class="theme-preview">
                    <div class="theme-bg ubuntu-green-bg"></div>
                    <div class="theme-dock ubuntu-green-dock"></div>
                    <div class="theme-window ubuntu-green-window"></div>
                </div>
                <span class="theme-name">Ubuntu 绿色</span>
            </div>
            <div class="theme-option" data-theme="ubuntu-blue">
                <div class="theme-preview">
                    <div class="theme-bg ubuntu-blue-bg"></div>
                    <div class="theme-dock ubuntu-blue-dock"></div>
                    <div class="theme-window ubuntu-blue-window"></div>
                </div>
                <span class="theme-name">Ubuntu 蓝色</span>
            </div>
        </div>
    </div>

    <!-- WeChat QR Code Modal -->
    <div class="wechat-modal" id="wechat-modal">
        <div class="wechat-modal-content">
            <div class="wechat-modal-header">
                <h3>微信联系站长</h3>
                <button class="wechat-modal-close" id="wechat-modal-close">×</button>
            </div>
            <div class="wechat-modal-body">
                <div class="qr-code-container">
                    <div class="qr-code-placeholder">
                        <svg width="120" height="120" viewBox="0 0 120 120" fill="none">
                            <!-- QR Code Pattern -->
                            <rect x="10" y="10" width="20" height="20" fill="#000"/>
                            <rect x="35" y="10" width="5" height="5" fill="#000"/>
                            <rect x="45" y="10" width="5" height="5" fill="#000"/>
                            <rect x="55" y="10" width="5" height="5" fill="#000"/>
                            <rect x="70" y="10" width="5" height="5" fill="#000"/>
                            <rect x="85" y="10" width="5" height="5" fill="#000"/>
                            <rect x="90" y="10" width="20" height="20" fill="#000"/>

                            <rect x="10" y="15" width="5" height="5" fill="#fff"/>
                            <rect x="20" y="15" width="5" height="5" fill="#fff"/>
                            <rect x="95" y="15" width="5" height="5" fill="#fff"/>
                            <rect x="105" y="15" width="5" height="5" fill="#fff"/>

                            <rect x="10" y="20" width="5" height="5" fill="#fff"/>
                            <rect x="20" y="20" width="5" height="5" fill="#fff"/>
                            <rect x="95" y="20" width="5" height="5" fill="#fff"/>
                            <rect x="105" y="20" width="5" height="5" fill="#fff"/>

                            <rect x="10" y="35" width="5" height="5" fill="#000"/>
                            <rect x="20" y="35" width="5" height="5" fill="#000"/>
                            <rect x="30" y="35" width="5" height="5" fill="#000"/>
                            <rect x="45" y="35" width="5" height="5" fill="#000"/>
                            <rect x="60" y="35" width="5" height="5" fill="#000"/>
                            <rect x="75" y="35" width="5" height="5" fill="#000"/>
                            <rect x="90" y="35" width="5" height="5" fill="#000"/>
                            <rect x="105" y="35" width="5" height="5" fill="#000"/>

                            <rect x="10" y="90" width="20" height="20" fill="#000"/>
                            <rect x="15" y="95" width="5" height="5" fill="#fff"/>
                            <rect x="20" y="95" width="5" height="5" fill="#fff"/>
                            <rect x="15" y="100" width="5" height="5" fill="#fff"/>
                            <rect x="20" y="100" width="5" height="5" fill="#fff"/>

                            <!-- More QR pattern elements -->
                            <rect x="50" y="50" width="20" height="20" fill="#000"/>
                            <rect x="55" y="55" width="5" height="5" fill="#fff"/>
                            <rect x="60" y="55" width="5" height="5" fill="#fff"/>
                            <rect x="55" y="60" width="5" height="5" fill="#fff"/>
                            <rect x="60" y="60" width="5" height="5" fill="#fff"/>
                        </svg>
                    </div>
                    <p class="qr-code-text">扫描二维码添加站长微信</p>
                    <p class="qr-code-note">微信号: qingyi-tech</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Music Player Modal -->
    <div class="music-modal" id="music-modal">
        <div class="music-modal-content">
            <div class="music-modal-header">
                <h3>音乐播放器</h3>
                <button class="music-modal-close" id="music-modal-close">×</button>
            </div>
            <div class="music-modal-body">
                <div class="music-player">
                    <div class="music-info">
                        <div class="music-title">Ubuntu 主题音乐</div>
                        <div class="music-artist">Linux Community</div>
                    </div>
                    <div class="music-controls">
                        <button class="music-btn" id="music-prev">⏮</button>
                        <button class="music-btn play-pause" id="music-play">▶</button>
                        <button class="music-btn" id="music-next">⏭</button>
                    </div>
                    <div class="music-progress">
                        <div class="progress-bar">
                            <div class="progress-fill"></div>
                        </div>
                        <div class="time-display">
                            <span class="current-time">0:00</span>
                            <span class="total-time">3:42</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
