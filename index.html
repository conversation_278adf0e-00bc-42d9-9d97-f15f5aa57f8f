<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>北京卿逸科技中心 - 创新科技，千年传承</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="logo">
                <svg width="32" height="32" viewBox="0 0 32 32" class="logo-icon">
                    <defs>
                        <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#0066FF;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#4285F4;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <!-- 简洁的几何图形 -->
                    <rect x="6" y="6" width="20" height="20" rx="4" fill="none" stroke="url(#logoGradient)" stroke-width="2"/>
                    <circle cx="16" cy="16" r="6" fill="url(#logoGradient)" opacity="0.2"/>
                    <circle cx="16" cy="16" r="3" fill="url(#logoGradient)"/>
                    <!-- 科技感的小点 -->
                    <circle cx="16" cy="8" r="1.5" fill="var(--accent-color)"/>
                    <circle cx="24" cy="16" r="1.5" fill="var(--accent-color)"/>
                    <circle cx="16" cy="24" r="1.5" fill="var(--accent-color)"/>
                    <circle cx="8" cy="16" r="1.5" fill="var(--accent-color)"/>
                </svg>
                <span class="logo-text">卿逸科技</span>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="#footer" class="nav-link">
                        <span class="nav-text">联系我们</span>
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Main Content - Full Screen -->
    <main class="main-content">
        <!-- Left Side - Hero Content -->
        <div class="hero-section">
            <div class="hero-content">
                <h1 class="hero-title">创新科技<br><span class="gradient-text">千年传承</span></h1>
                <p class="hero-subtitle">北京卿逸科技中心致力于将传统智慧与现代科技完美融合，为企业数字化转型提供前沿解决方案</p>
                <div class="hero-buttons">
                    <button class="btn-primary">了解更多</button>
                    <button class="btn-secondary">观看演示</button>
                </div>
            </div>
            <div class="hero-visual">
                <div class="floating-elements">
                    <div class="element element-1"></div>
                    <div class="element element-2"></div>
                    <div class="element element-3"></div>
                </div>
            </div>
        </div>

        <!-- Right Side - Content Tabs -->
        <div class="content-section">
            <div class="tab-navigation">
                <button class="tab-btn active" data-tab="products">核心产品</button>
                <button class="tab-btn" data-tab="solutions">解决方案</button>
                <button class="tab-btn" data-tab="about">关于我们</button>
            </div>

            <div class="tab-content">
                <!-- Products Tab -->
                <div id="products" class="tab-panel active">
                    <div class="products-grid">
                        <div class="product-card">
                            <div class="product-icon">🚀</div>
                            <h3>智能云平台</h3>
                            <p>基于AI的企业级云计算解决方案，提供弹性扩展和智能运维</p>
                            <div class="tech-stats">
                                <span class="stat-item">99.9% 可用性</span>
                                <span class="stat-item">毫秒级响应</span>
                            </div>
                        </div>
                        <div class="product-card">
                            <div class="product-icon">🔮</div>
                            <h3>数据分析引擎</h3>
                            <p>实时大数据处理与可视化分析，助力企业决策智能化</p>
                            <div class="tech-stats">
                                <span class="stat-item">PB级处理</span>
                                <span class="stat-item">实时分析</span>
                            </div>
                        </div>
                        <div class="product-card">
                            <div class="product-icon">⚡</div>
                            <h3>自动化平台</h3>
                            <p>端到端业务流程自动化，提升企业运营效率</p>
                            <div class="tech-stats">
                                <span class="stat-item">80% 效率提升</span>
                                <span class="stat-item">零代码配置</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Solutions Tab -->
                <div id="solutions" class="tab-panel">
                    <div class="solutions-grid">
                        <div class="solution-card">
                            <div class="solution-header">
                                <div class="solution-icon">🏢</div>
                                <h3>企业数字化转型</h3>
                            </div>
                            <p>为传统企业提供全方位数字化转型咨询与实施服务</p>
                            <ul class="solution-features">
                                <li>业务流程数字化</li>
                                <li>数据驱动决策</li>
                                <li>智能运营管理</li>
                            </ul>
                        </div>
                        <div class="solution-card">
                            <div class="solution-header">
                                <div class="solution-icon">☁️</div>
                                <h3>云原生架构</h3>
                            </div>
                            <p>基于云原生技术栈，构建可扩展、高可用的现代化应用架构</p>
                            <ul class="solution-features">
                                <li>微服务架构设计</li>
                                <li>容器化部署</li>
                                <li>DevOps 自动化</li>
                            </ul>
                        </div>
                        <div class="solution-card">
                            <div class="solution-header">
                                <div class="solution-icon">🤖</div>
                                <h3>AI 智能应用</h3>
                            </div>
                            <p>将人工智能技术融入业务场景，提升企业智能化水平</p>
                            <ul class="solution-features">
                                <li>智能客服系统</li>
                                <li>预测性分析</li>
                                <li>自动化决策</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- About Tab -->
                <div id="about" class="tab-panel">
                    <div class="about-content">
                        <div class="about-text">
                            <h3>关于卿逸科技</h3>
                            <p class="about-description">
                                北京卿逸科技中心成立于2024年，是一家专注于企业数字化转型的创新科技公司。
                                我们秉承"创新科技，千年传承"的理念，致力于将前沿技术与传统智慧相结合，
                                为客户提供卓越的技术解决方案。
                            </p>
                            <div class="about-stats">
                                <div class="stat">
                                    <div class="stat-number">50+</div>
                                    <div class="stat-label">成功项目</div>
                                </div>
                                <div class="stat">
                                    <div class="stat-number">20+</div>
                                    <div class="stat-label">合作伙伴</div>
                                </div>
                                <div class="stat">
                                    <div class="stat-number">99%</div>
                                    <div class="stat-label">客户满意度</div>
                                </div>
                            </div>
                        </div>
                        <div class="about-image">
                            <div class="image-placeholder">
                                <div class="tech-grid">
                                    <div class="grid-item"></div>
                                    <div class="grid-item"></div>
                                    <div class="grid-item"></div>
                                    <div class="grid-item"></div>
                                    <div class="grid-item active"></div>
                                    <div class="grid-item"></div>
                                    <div class="grid-item"></div>
                                    <div class="grid-item active"></div>
                                    <div class="grid-item"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer id="footer" class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-info">
                    <div class="logo">
                        <svg width="32" height="32" viewBox="0 0 40 40">
                            <circle cx="20" cy="20" r="18" fill="none" stroke="currentColor" stroke-width="2"/>
                            <path d="M12 20 L20 12 L28 20 L20 28 Z" fill="currentColor"/>
                            <circle cx="20" cy="20" r="3" fill="white"/>
                        </svg>
                        <span>北京卿逸科技中心</span>
                    </div>
                    <p>创新科技，千年传承</p>
                </div>
                <div class="footer-links">
                    <div class="link-group">
                        <h4>产品</h4>
                        <a href="#">智能云平台</a>
                        <a href="#">数据分析</a>
                        <a href="#">自动化平台</a>
                    </div>
                    <div class="link-group">
                        <h4>服务</h4>
                        <a href="#">技术支持</a>
                        <a href="#">咨询服务</a>
                        <a href="#">培训认证</a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 北京卿逸科技中心. 保留所有权利.</p>
                <p>备案号：<a href="https://beian.miit.gov.cn/" target="_blank">京ICP备2025107618号-1</a></p>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>