<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>卿逸科技 - Ubuntu风格</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Ubuntu:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Ubuntu Style Desktop -->
    <div class="ubuntu-desktop">
        <!-- Top Bar -->
        <div class="top-bar">
            <div class="top-bar-left">
                <div class="task-manager-button" id="task-manager-btn">
                    <div class="task-icon">
                        <div class="task-icon-bar"></div>
                        <div class="task-icon-bar"></div>
                        <div class="task-icon-bar"></div>
                    </div>
                    <span>任务管理</span>
                </div>
            </div>
            <div class="top-bar-center">
                <div class="clock">7月16日 20:47</div>
            </div>
            <div class="top-bar-right">
                <div class="system-indicators">
                    <div class="indicator">🔊</div>
                    <div class="indicator">📶</div>
                    <div class="indicator">🔋</div>
                    <div class="indicator">⚙️</div>
                </div>
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="desktop-content">
            <!-- Main Content - Desktop Background -->
            <div class="main-content">
                <!-- Desktop Background -->
                <div class="desktop-background">
                    <div class="desktop-wallpaper">
                        <div class="welcome-text">
                            <h1>欢迎使用卿逸科技系统</h1>
                            <p>点击应用菜单或底部Dock开始探索</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Mac Style Dock -->
            <div class="mac-dock">
                <div class="dock-container">
                    <div class="dock-item active" data-view="home">
                        <div class="dock-icon">
                            <svg width="32" height="32" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                            </svg>
                        </div>
                        <div class="dock-label">首页</div>
                    </div>
                    <div class="dock-item" data-view="products">
                        <div class="dock-icon">
                            <svg width="32" height="32" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M7 4V2C7 1.45 7.45 1 8 1H16C16.55 1 17 1.45 17 2V4H20C20.55 4 21 4.45 21 5S20.55 6 20 6H19V19C19 20.1 18.1 21 17 21H7C5.9 21 5 20.1 5 19V6H4C3.45 6 3 5.55 3 5S3.45 4 4 4H7ZM9 3V4H15V3H9ZM7 6V19H17V6H7Z"/>
                                <path d="M9 8V17H11V8H9ZM13 8V17H15V8H13Z"/>
                            </svg>
                        </div>
                        <div class="dock-label">产品</div>
                    </div>
                    <div class="dock-item" data-view="solutions">
                        <div class="dock-icon">
                            <svg width="32" height="32" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M22.7 19L13.6 9.9C14.5 7.6 14 4.9 12.1 3C10.1 1 7.1 1 5.1 3S3.1 7.9 5.1 9.9C7 11.8 9.6 12.3 11.9 11.4L21 20.5C21.4 20.9 21.4 21.6 21 22C20.6 21.6 19.9 21.6 19.5 21.2L22.7 19ZM6.5 8.5C5.1 7.1 5.1 4.9 6.5 3.5S10.9 2.1 12.3 3.5S13.7 7.9 12.3 9.3S7.9 9.9 6.5 8.5Z"/>
                                <path d="M15.5 4L20.5 9L19 10.5L14 5.5L15.5 4Z"/>
                            </svg>
                        </div>
                        <div class="dock-label">方案</div>
                    </div>
                    <div class="dock-item" data-view="about">
                        <div class="dock-icon">
                            <svg width="32" height="32" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2C6.48 2 2 6.48 2 12S6.48 22 12 22 22 17.52 22 12 17.52 2 12 2ZM13 17H11V11H13V17ZM13 9H11V7H13V9Z"/>
                            </svg>
                        </div>
                        <div class="dock-label">关于</div>
                    </div>
                    <div class="dock-item" data-view="contact">
                        <div class="dock-icon">
                            <svg width="32" height="32" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M6.62 10.79C8.06 13.62 10.38 15.94 13.21 17.38L15.41 15.18C15.69 14.9 16.08 14.82 16.43 14.93C17.55 15.3 18.75 15.5 20 15.5C20.55 15.5 21 15.95 21 16.5V20C21 20.55 20.55 21 20 21C10.61 21 3 13.39 3 4C3 3.45 3.45 3 4 3H7.5C8.05 3 8.5 3.45 8.5 4C8.5 5.25 8.7 6.45 9.07 7.57C9.18 7.92 9.1 8.31 8.82 8.59L6.62 10.79Z"/>
                            </svg>
                        </div>
                        <div class="dock-label">联系</div>
                    </div>

                    <div class="dock-separator"></div>

                    <div class="dock-item" data-app="analytics">
                        <div class="dock-icon">
                            <svg width="32" height="32" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M19 3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3ZM9 17H7V10H9V17ZM13 17H11V7H13V17ZM17 17H15V13H17V17Z"/>
                            </svg>
                        </div>
                        <div class="dock-label">分析</div>
                    </div>
                    <div class="dock-item" data-app="cloud">
                        <div class="dock-icon">
                            <svg width="32" height="32" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M19.35 10.04C18.67 6.59 15.64 4 12 4C9.11 4 6.6 5.64 5.35 8.04C2.34 8.36 0 10.91 0 14C0 17.31 2.69 20 6 20H19C21.76 20 24 17.76 24 15C24 12.36 21.95 10.22 19.35 10.04Z"/>
                            </svg>
                        </div>
                        <div class="dock-label">云端</div>
                    </div>
                    <div class="dock-item" data-app="ai">
                        <div class="dock-icon">
                            <svg width="32" height="32" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M20 9V7C20 5.9 19.1 5 18 5H16L14.5 3.5C14.1 3.1 13.6 2.9 13.1 2.9H10.9C10.4 2.9 9.9 3.1 9.5 3.5L8 5H6C4.9 5 4 5.9 4 7V9C4 9.6 4.4 10 5 10S6 9.6 6 9V7H8.8L10.3 5.5H13.7L15.2 7H18V9C18 9.6 18.4 10 19 10S20 9.6 20 9Z"/>
                                <path d="M12 8C9.8 8 8 9.8 8 12S9.8 16 12 16 16 14.2 16 12 14.2 8 12 8ZM12 14C10.9 14 10 13.1 10 12S10.9 10 12 10 14 10.9 14 12 13.1 14 12 14Z"/>
                                <path d="M12 18C8.7 18 6 15.3 6 12H4C4 16.4 7.6 20 12 20S20 16.4 20 12H18C18 15.3 15.3 18 12 18Z"/>
                            </svg>
                        </div>
                        <div class="dock-label">AI</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Window Applications -->
    <!-- Home Window -->
    <div class="app-window" id="home-window" data-app="home">
        <div class="window-header ubuntu-header">
            <div class="window-title">卿逸科技 - 创新科技，千年传承</div>
            <div class="window-controls ubuntu-controls">
                <div class="ubuntu-control minimize" data-action="minimize" title="最小化" onclick="window.windowMinimize('home')">
                    <svg width="16" height="16" viewBox="0 0 16 16">
                        <line x1="4" y1="8" x2="12" y2="8" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </div>
                <div class="ubuntu-control maximize" data-action="maximize" title="最大化" onclick="window.windowMaximize('home')">
                    <svg width="16" height="16" viewBox="0 0 16 16">
                        <rect x="3" y="3" width="10" height="10" fill="none" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </div>
                <div class="ubuntu-control close" data-action="close" title="关闭" onclick="window.windowClose('home')">
                    <svg width="16" height="16" viewBox="0 0 16 16">
                        <line x1="4" y1="4" x2="12" y2="12" stroke="currentColor" stroke-width="2"/>
                        <line x1="12" y1="4" x2="4" y2="12" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </div>
            </div>
        </div>
        
        <div class="window-content">
            <div class="hero-section">
                <div class="hero-text">
                    <h1>北京卿逸科技中心</h1>
                    <h2>创新科技 · 千年传承</h2>
                    <p>专注于企业数字化转型的创新科技公司，致力于将前沿技术与传统智慧相结合</p>
                    <div class="action-buttons">
                        <button class="ubuntu-btn primary" data-open="products">了解产品</button>
                        <button class="ubuntu-btn secondary" data-open="about">关于我们</button>
                    </div>
                </div>
                <div class="hero-visual">
                    <div class="ubuntu-logo-large">
                        <div class="logo-ring"></div>
                        <div class="logo-center">卿逸</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Products Window -->
    <div class="app-window" id="products-window" data-app="products">
        <div class="window-header ubuntu-header">
            <div class="window-title">核心产品</div>
            <div class="window-controls ubuntu-controls">
                <div class="ubuntu-control minimize" data-action="minimize" title="最小化" onclick="window.windowMinimize('products')">
                    <svg width="16" height="16" viewBox="0 0 16 16">
                        <line x1="4" y1="8" x2="12" y2="8" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </div>
                <div class="ubuntu-control maximize" data-action="maximize" title="最大化" onclick="window.windowMaximize('products')">
                    <svg width="16" height="16" viewBox="0 0 16 16">
                        <rect x="3" y="3" width="10" height="10" fill="none" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </div>
                <div class="ubuntu-control close" data-action="close" title="关闭" onclick="window.windowClose('products')">
                    <svg width="16" height="16" viewBox="0 0 16 16">
                        <line x1="4" y1="4" x2="12" y2="12" stroke="currentColor" stroke-width="2"/>
                        <line x1="12" y1="4" x2="4" y2="12" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </div>
            </div>
        </div>
        
        <div class="window-content">
            <div class="products-grid">
                <div class="product-card ubuntu-card">
                    <div class="card-header">
                        <div class="product-icon">🚀</div>
                        <h3>智能云平台</h3>
                    </div>
                    <div class="card-content">
                        <p>基于AI的企业级云计算解决方案，提供弹性扩展和智能运维</p>
                        <div class="features">
                            <span class="feature-tag">99.9% 可用性</span>
                            <span class="feature-tag">毫秒级响应</span>
                        </div>
                    </div>
                </div>
                
                <div class="product-card ubuntu-card">
                    <div class="card-header">
                        <div class="product-icon">🔮</div>
                        <h3>数据分析引擎</h3>
                    </div>
                    <div class="card-content">
                        <p>实时大数据处理与可视化分析，助力企业决策智能化</p>
                        <div class="features">
                            <span class="feature-tag">PB级处理</span>
                            <span class="feature-tag">实时分析</span>
                        </div>
                    </div>
                </div>
                
                <div class="product-card ubuntu-card">
                    <div class="card-header">
                        <div class="product-icon">⚡</div>
                        <h3>自动化平台</h3>
                    </div>
                    <div class="card-content">
                        <p>端到端业务流程自动化，提升企业运营效率</p>
                        <div class="features">
                            <span class="feature-tag">80% 效率提升</span>
                            <span class="feature-tag">零代码配置</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Solutions Window -->
    <div class="app-window" id="solutions-window" data-app="solutions">
        <div class="window-header ubuntu-header">
            <div class="window-title">解决方案</div>
            <div class="window-controls ubuntu-controls">
                <div class="ubuntu-control minimize" data-action="minimize" title="最小化" onclick="window.windowMinimize('solutions')">
                    <svg width="16" height="16" viewBox="0 0 16 16">
                        <line x1="4" y1="8" x2="12" y2="8" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </div>
                <div class="ubuntu-control maximize" data-action="maximize" title="最大化" onclick="window.windowMaximize('solutions')">
                    <svg width="16" height="16" viewBox="0 0 16 16">
                        <rect x="3" y="3" width="10" height="10" fill="none" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </div>
                <div class="ubuntu-control close" data-action="close" title="关闭" onclick="window.windowClose('solutions')">
                    <svg width="16" height="16" viewBox="0 0 16 16">
                        <line x1="4" y1="4" x2="12" y2="12" stroke="currentColor" stroke-width="2"/>
                        <line x1="12" y1="4" x2="4" y2="12" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </div>
            </div>
        </div>

        <div class="window-content">
            <div class="solutions-list">
                <div class="solution-item ubuntu-card">
                    <div class="solution-header">
                        <div class="solution-icon">🏢</div>
                        <div class="solution-info">
                            <h3>企业数字化转型</h3>
                            <p>为传统企业提供全方位数字化转型咨询与实施服务</p>
                        </div>
                    </div>
                    <div class="solution-details">
                        <ul>
                            <li>业务流程数字化</li>
                            <li>数据驱动决策</li>
                            <li>智能运营管理</li>
                        </ul>
                    </div>
                </div>

                <div class="solution-item ubuntu-card">
                    <div class="solution-header">
                        <div class="solution-icon">☁️</div>
                        <div class="solution-info">
                            <h3>云原生架构</h3>
                            <p>基于云原生技术栈，构建可扩展、高可用的现代化应用架构</p>
                        </div>
                    </div>
                    <div class="solution-details">
                        <ul>
                            <li>微服务架构设计</li>
                            <li>容器化部署</li>
                            <li>DevOps 自动化</li>
                        </ul>
                    </div>
                </div>

                <div class="solution-item ubuntu-card">
                    <div class="solution-header">
                        <div class="solution-icon">🤖</div>
                        <div class="solution-info">
                            <h3>AI 智能应用</h3>
                            <p>将人工智能技术融入业务场景，提升企业智能化水平</p>
                        </div>
                    </div>
                    <div class="solution-details">
                        <ul>
                            <li>智能客服系统</li>
                            <li>预测性分析</li>
                            <li>自动化决策</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- About Window -->
    <div class="app-window" id="about-window" data-app="about">
        <div class="window-header ubuntu-header">
            <div class="window-title">关于我们</div>
            <div class="window-controls ubuntu-controls">
                <div class="ubuntu-control minimize" data-action="minimize" title="最小化" onclick="window.windowMinimize('about')">
                    <svg width="16" height="16" viewBox="0 0 16 16">
                        <line x1="4" y1="8" x2="12" y2="8" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </div>
                <div class="ubuntu-control maximize" data-action="maximize" title="最大化" onclick="window.windowMaximize('about')">
                    <svg width="16" height="16" viewBox="0 0 16 16">
                        <rect x="3" y="3" width="10" height="10" fill="none" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </div>
                <div class="ubuntu-control close" data-action="close" title="关闭" onclick="window.windowClose('about')">
                    <svg width="16" height="16" viewBox="0 0 16 16">
                        <line x1="4" y1="4" x2="12" y2="12" stroke="currentColor" stroke-width="2"/>
                        <line x1="12" y1="4" x2="4" y2="12" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </div>
            </div>
        </div>

        <div class="window-content">
            <div class="about-content">
                <div class="company-info ubuntu-card">
                    <h3>公司简介</h3>
                    <p>北京卿逸科技中心成立于2024年，是一家专注于企业数字化转型的创新科技公司。我们秉承"创新科技，千年传承"的理念，致力于将前沿技术与传统智慧相结合。</p>

                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-number">50+</div>
                            <div class="stat-label">成功项目</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">20+</div>
                            <div class="stat-label">合作伙伴</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">99%</div>
                            <div class="stat-label">客户满意度</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Contact Window -->
    <div class="app-window" id="contact-window" data-app="contact">
        <div class="window-header ubuntu-header">
            <div class="window-title">联系我们</div>
            <div class="window-controls ubuntu-controls">
                <div class="ubuntu-control minimize" data-action="minimize" title="最小化" onclick="window.windowMinimize('contact')">
                    <svg width="16" height="16" viewBox="0 0 16 16">
                        <line x1="4" y1="8" x2="12" y2="8" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </div>
                <div class="ubuntu-control maximize" data-action="maximize" title="最大化" onclick="window.windowMaximize('contact')">
                    <svg width="16" height="16" viewBox="0 0 16 16">
                        <rect x="3" y="3" width="10" height="10" fill="none" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </div>
                <div class="ubuntu-control close" data-action="close" title="关闭" onclick="window.windowClose('contact')">
                    <svg width="16" height="16" viewBox="0 0 16 16">
                        <line x1="4" y1="4" x2="12" y2="12" stroke="currentColor" stroke-width="2"/>
                        <line x1="12" y1="4" x2="4" y2="12" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </div>
            </div>
        </div>

        <div class="window-content">
            <div class="contact-info ubuntu-card">
                <h3>联系方式</h3>
                <div class="contact-details">
                    <div class="contact-item">
                        <strong>公司名称：</strong>北京卿逸科技中心
                    </div>
                    <div class="contact-item">
                        <strong>备案号：</strong>京ICP备2025107618号-1
                    </div>
                    <div class="contact-item">
                        <strong>成立时间：</strong>2024年
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Taskbar for minimized windows -->
    <div class="taskbar" id="taskbar">
        <div class="taskbar-items"></div>
    </div>

    <script src="script.js"></script>
</body>
</html>
