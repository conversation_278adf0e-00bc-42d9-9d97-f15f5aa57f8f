<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>卿逸科技 - Ubuntu风格</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Ubuntu:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Ubuntu Style Desktop -->
    <div class="ubuntu-desktop">
        <!-- Top Bar -->
        <div class="top-bar">
            <div class="top-bar-left">
                <div class="task-manager-button" id="task-manager-btn">
                    <div class="task-icon">
                        <div class="task-icon-bar"></div>
                        <div class="task-icon-bar"></div>
                        <div class="task-icon-bar"></div>
                    </div>
                    <span>任务管理</span>
                </div>
            </div>
            <div class="top-bar-center">
                <div class="clock">7月16日 20:47</div>
            </div>
            <div class="top-bar-right">
                <div class="system-indicators">
                    <a href="https://beian.miit.gov.cn/" target="_blank" class="beian-link" title="工信部备案查询">
                        京ICP备2025107618号-1
                    </a>
                    <div class="theme-switcher" id="theme-switcher" title="切换主题">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                            <circle cx="12" cy="12" r="4" stroke="currentColor" stroke-width="2"/>
                            <path d="M12 2V4" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                            <path d="M12 20V22" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                            <path d="M4.93 4.93L6.34 6.34" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                            <path d="M17.66 17.66L19.07 19.07" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                            <path d="M2 12H4" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                            <path d="M20 12H22" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                            <path d="M6.34 17.66L4.93 19.07" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                            <path d="M19.07 4.93L17.66 6.34" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                        </svg>
                    </div>
                    <div class="indicator">🔊</div>
                    <div class="indicator">📶</div>
                    <div class="indicator">🔋</div>
                    <div class="indicator">⚙️</div>
                </div>
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="desktop-content">
            <!-- Main Content - Desktop Background -->
            <div class="main-content">
                <!-- Desktop Background -->
                <div class="desktop-background">
                    <div class="desktop-wallpaper">
                        <div class="welcome-text">
                            <h1>欢迎使用卿逸科技系统</h1>
                            <p>点击应用菜单或底部Dock开始探索</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Mac Style Dock -->
            <div class="mac-dock">
                <div class="dock-container">
                    <div class="dock-item active" data-view="home">
                        <div class="dock-icon">
                            <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
                                <path d="M3 12L5 10M5 10L12 3L19 10M5 10V20C5 20.5523 5.44772 21 6 21H9M19 10L21 12M19 10V20C19 20.5523 18.5523 21 18 21H15M9 21C9.55228 21 10 20.5523 10 20V16C10 15.4477 10.4477 15 11 15H13C13.5523 15 14 15.4477 14 16V20C14 20.5523 14.4477 21 15 21M9 21H15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="dock-label">首页</div>
                    </div>
                    <div class="dock-item" data-view="products">
                        <div class="dock-icon">
                            <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
                                <path d="M20 7L12 3L4 7M20 7L12 11M20 7V17L12 21M12 11L4 7M12 11V21M4 7V17L12 21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M16 5L8 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="dock-label">产品</div>
                    </div>
                    <div class="dock-item" data-view="solutions">
                        <div class="dock-icon">
                            <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
                                <path d="M14.7 6.3C15.1 5.9 15.1 5.3 14.7 4.9C14.3 4.5 13.7 4.5 13.3 4.9L9 9.2C8.6 9.6 8.6 10.2 9 10.6L13.3 14.9C13.7 15.3 14.3 15.3 14.7 14.9C15.1 14.5 15.1 13.9 14.7 13.5L11.4 10.2L14.7 6.9V6.3Z" fill="currentColor"/>
                                <path d="M9.3 6.3C8.9 5.9 8.9 5.3 9.3 4.9C9.7 4.5 10.3 4.5 10.7 4.9L15 9.2C15.4 9.6 15.4 10.2 15 10.6L10.7 14.9C10.3 15.3 9.7 15.3 9.3 14.9C8.9 14.5 8.9 13.9 9.3 13.5L12.6 10.2L9.3 6.9V6.3Z" fill="currentColor"/>
                                <circle cx="12" cy="12" r="9" stroke="currentColor" stroke-width="2"/>
                                <path d="M8 12L16 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                <path d="M12 8L12 16" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                            </svg>
                        </div>
                        <div class="dock-label">方案</div>
                    </div>
                    <div class="dock-item" data-view="about">
                        <div class="dock-icon">
                            <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
                                <circle cx="12" cy="12" r="9" stroke="currentColor" stroke-width="2"/>
                                <path d="M12 8V12L15 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <circle cx="12" cy="6" r="1" fill="currentColor"/>
                            </svg>
                        </div>
                        <div class="dock-label">关于</div>
                    </div>
                    <div class="dock-item" data-view="contact">
                        <div class="dock-icon">
                            <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
                                <path d="M22 12C22 17.5228 17.5228 22 12 22C10.1786 22 8.47087 21.4135 7.09899 20.4279L2 22L3.57212 16.901C2.58651 15.5291 2 13.8214 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M8 12H8.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M12 12H12.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M16 12H16.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="dock-label">联系</div>
                    </div>

                    <div class="dock-separator"></div>

                    <div class="dock-item" data-app="analytics">
                        <div class="dock-icon">
                            <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
                                <path d="M3 3V21H21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M7 16L12 11L16 15L21 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <circle cx="7" cy="16" r="1" fill="currentColor"/>
                                <circle cx="12" cy="11" r="1" fill="currentColor"/>
                                <circle cx="16" cy="15" r="1" fill="currentColor"/>
                                <circle cx="21" cy="10" r="1" fill="currentColor"/>
                            </svg>
                        </div>
                        <div class="dock-label">分析</div>
                    </div>
                    <div class="dock-item" data-app="cloud">
                        <div class="dock-icon">
                            <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
                                <path d="M18 10H20C21.1046 10 22 10.8954 22 12C22 13.1046 21.1046 14 20 14H18M6 14H4C2.89543 14 2 13.1046 2 12C2 10.8954 2.89543 10 4 10H6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M8 12C8 8.68629 10.6863 6 14 6C17.3137 6 20 8.68629 20 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M8 16C8 17.1046 8.89543 18 10 18H18C19.1046 18 20 17.1046 20 16" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="dock-label">云端</div>
                    </div>
                    <div class="dock-item" data-app="ai">
                        <div class="dock-icon">
                            <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
                                <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                                <path d="M12 1V3" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                <path d="M12 21V23" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                <path d="M4.22 4.22L5.64 5.64" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                <path d="M18.36 18.36L19.78 19.78" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                <path d="M1 12H3" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                <path d="M21 12H23" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                <path d="M4.22 19.78L5.64 18.36" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                <path d="M18.36 5.64L19.78 4.22" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                            </svg>
                        </div>
                        <div class="dock-label">AI</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Window Applications -->
    <!-- Home Window -->
    <div class="app-window" id="home-window" data-app="home">
        <div class="window-header ubuntu-header">
            <div class="window-title">卿逸科技 - 创新科技，千年传承</div>
            <div class="window-controls ubuntu-controls">
                <div class="ubuntu-control minimize" data-action="minimize" title="最小化" onclick="window.windowMinimize('home')">
                    <svg width="16" height="16" viewBox="0 0 16 16">
                        <line x1="4" y1="8" x2="12" y2="8" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </div>
                <div class="ubuntu-control maximize" data-action="maximize" title="最大化" onclick="window.windowMaximize('home')">
                    <svg width="16" height="16" viewBox="0 0 16 16">
                        <rect x="3" y="3" width="10" height="10" fill="none" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </div>
                <div class="ubuntu-control close" data-action="close" title="关闭" onclick="window.windowClose('home')">
                    <svg width="16" height="16" viewBox="0 0 16 16">
                        <line x1="4" y1="4" x2="12" y2="12" stroke="currentColor" stroke-width="2"/>
                        <line x1="12" y1="4" x2="4" y2="12" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </div>
            </div>
        </div>
        
        <div class="window-content">
            <div class="hero-section">
                <div class="hero-text">
                    <h1>北京卿逸科技中心</h1>
                    <h2>创新科技 · 千年传承</h2>
                    <p>专注于企业数字化转型的创新科技公司，致力于将前沿技术与传统智慧相结合</p>
                    <div class="action-buttons">
                        <button class="ubuntu-btn primary" data-open="products">了解产品</button>
                        <button class="ubuntu-btn secondary" data-open="about">关于我们</button>
                    </div>
                </div>
                <div class="hero-visual">
                    <div class="ubuntu-logo-large">
                        <div class="logo-ring"></div>
                        <div class="logo-center">卿逸</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Products Window -->
    <div class="app-window" id="products-window" data-app="products">
        <div class="window-header ubuntu-header">
            <div class="window-title">核心产品</div>
            <div class="window-controls ubuntu-controls">
                <div class="ubuntu-control minimize" data-action="minimize" title="最小化" onclick="window.windowMinimize('products')">
                    <svg width="16" height="16" viewBox="0 0 16 16">
                        <line x1="4" y1="8" x2="12" y2="8" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </div>
                <div class="ubuntu-control maximize" data-action="maximize" title="最大化" onclick="window.windowMaximize('products')">
                    <svg width="16" height="16" viewBox="0 0 16 16">
                        <rect x="3" y="3" width="10" height="10" fill="none" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </div>
                <div class="ubuntu-control close" data-action="close" title="关闭" onclick="window.windowClose('products')">
                    <svg width="16" height="16" viewBox="0 0 16 16">
                        <line x1="4" y1="4" x2="12" y2="12" stroke="currentColor" stroke-width="2"/>
                        <line x1="12" y1="4" x2="4" y2="12" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </div>
            </div>
        </div>
        
        <div class="window-content">
            <div class="products-grid">
                <div class="product-card ubuntu-card">
                    <div class="card-header">
                        <div class="product-icon">🚀</div>
                        <h3>智能云平台</h3>
                    </div>
                    <div class="card-content">
                        <p>基于AI的企业级云计算解决方案，提供弹性扩展和智能运维</p>
                        <div class="features">
                            <span class="feature-tag">99.9% 可用性</span>
                            <span class="feature-tag">毫秒级响应</span>
                        </div>
                    </div>
                </div>
                
                <div class="product-card ubuntu-card">
                    <div class="card-header">
                        <div class="product-icon">🔮</div>
                        <h3>数据分析引擎</h3>
                    </div>
                    <div class="card-content">
                        <p>实时大数据处理与可视化分析，助力企业决策智能化</p>
                        <div class="features">
                            <span class="feature-tag">PB级处理</span>
                            <span class="feature-tag">实时分析</span>
                        </div>
                    </div>
                </div>
                
                <div class="product-card ubuntu-card">
                    <div class="card-header">
                        <div class="product-icon">⚡</div>
                        <h3>自动化平台</h3>
                    </div>
                    <div class="card-content">
                        <p>端到端业务流程自动化，提升企业运营效率</p>
                        <div class="features">
                            <span class="feature-tag">80% 效率提升</span>
                            <span class="feature-tag">零代码配置</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Solutions Window -->
    <div class="app-window" id="solutions-window" data-app="solutions">
        <div class="window-header ubuntu-header">
            <div class="window-title">解决方案</div>
            <div class="window-controls ubuntu-controls">
                <div class="ubuntu-control minimize" data-action="minimize" title="最小化" onclick="window.windowMinimize('solutions')">
                    <svg width="16" height="16" viewBox="0 0 16 16">
                        <line x1="4" y1="8" x2="12" y2="8" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </div>
                <div class="ubuntu-control maximize" data-action="maximize" title="最大化" onclick="window.windowMaximize('solutions')">
                    <svg width="16" height="16" viewBox="0 0 16 16">
                        <rect x="3" y="3" width="10" height="10" fill="none" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </div>
                <div class="ubuntu-control close" data-action="close" title="关闭" onclick="window.windowClose('solutions')">
                    <svg width="16" height="16" viewBox="0 0 16 16">
                        <line x1="4" y1="4" x2="12" y2="12" stroke="currentColor" stroke-width="2"/>
                        <line x1="12" y1="4" x2="4" y2="12" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </div>
            </div>
        </div>

        <div class="window-content">
            <div class="solutions-list">
                <div class="solution-item ubuntu-card">
                    <div class="solution-header">
                        <div class="solution-icon">🏢</div>
                        <div class="solution-info">
                            <h3>企业数字化转型</h3>
                            <p>为传统企业提供全方位数字化转型咨询与实施服务</p>
                        </div>
                    </div>
                    <div class="solution-details">
                        <ul>
                            <li>业务流程数字化</li>
                            <li>数据驱动决策</li>
                            <li>智能运营管理</li>
                        </ul>
                    </div>
                </div>

                <div class="solution-item ubuntu-card">
                    <div class="solution-header">
                        <div class="solution-icon">☁️</div>
                        <div class="solution-info">
                            <h3>云原生架构</h3>
                            <p>基于云原生技术栈，构建可扩展、高可用的现代化应用架构</p>
                        </div>
                    </div>
                    <div class="solution-details">
                        <ul>
                            <li>微服务架构设计</li>
                            <li>容器化部署</li>
                            <li>DevOps 自动化</li>
                        </ul>
                    </div>
                </div>

                <div class="solution-item ubuntu-card">
                    <div class="solution-header">
                        <div class="solution-icon">🤖</div>
                        <div class="solution-info">
                            <h3>AI 智能应用</h3>
                            <p>将人工智能技术融入业务场景，提升企业智能化水平</p>
                        </div>
                    </div>
                    <div class="solution-details">
                        <ul>
                            <li>智能客服系统</li>
                            <li>预测性分析</li>
                            <li>自动化决策</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- About Window -->
    <div class="app-window" id="about-window" data-app="about">
        <div class="window-header ubuntu-header">
            <div class="window-title">关于我们</div>
            <div class="window-controls ubuntu-controls">
                <div class="ubuntu-control minimize" data-action="minimize" title="最小化" onclick="window.windowMinimize('about')">
                    <svg width="16" height="16" viewBox="0 0 16 16">
                        <line x1="4" y1="8" x2="12" y2="8" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </div>
                <div class="ubuntu-control maximize" data-action="maximize" title="最大化" onclick="window.windowMaximize('about')">
                    <svg width="16" height="16" viewBox="0 0 16 16">
                        <rect x="3" y="3" width="10" height="10" fill="none" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </div>
                <div class="ubuntu-control close" data-action="close" title="关闭" onclick="window.windowClose('about')">
                    <svg width="16" height="16" viewBox="0 0 16 16">
                        <line x1="4" y1="4" x2="12" y2="12" stroke="currentColor" stroke-width="2"/>
                        <line x1="12" y1="4" x2="4" y2="12" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </div>
            </div>
        </div>

        <div class="window-content">
            <div class="about-content">
                <div class="company-info ubuntu-card">
                    <h3>公司简介</h3>
                    <p>北京卿逸科技中心成立于2024年，是一家专注于企业数字化转型的创新科技公司。我们秉承"创新科技，千年传承"的理念，致力于将前沿技术与传统智慧相结合。</p>

                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-number">50+</div>
                            <div class="stat-label">成功项目</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">20+</div>
                            <div class="stat-label">合作伙伴</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">99%</div>
                            <div class="stat-label">客户满意度</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Contact Window -->
    <div class="app-window" id="contact-window" data-app="contact">
        <div class="window-header ubuntu-header">
            <div class="window-title">联系我们</div>
            <div class="window-controls ubuntu-controls">
                <div class="ubuntu-control minimize" data-action="minimize" title="最小化" onclick="window.windowMinimize('contact')">
                    <svg width="16" height="16" viewBox="0 0 16 16">
                        <line x1="4" y1="8" x2="12" y2="8" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </div>
                <div class="ubuntu-control maximize" data-action="maximize" title="最大化" onclick="window.windowMaximize('contact')">
                    <svg width="16" height="16" viewBox="0 0 16 16">
                        <rect x="3" y="3" width="10" height="10" fill="none" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </div>
                <div class="ubuntu-control close" data-action="close" title="关闭" onclick="window.windowClose('contact')">
                    <svg width="16" height="16" viewBox="0 0 16 16">
                        <line x1="4" y1="4" x2="12" y2="12" stroke="currentColor" stroke-width="2"/>
                        <line x1="12" y1="4" x2="4" y2="12" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </div>
            </div>
        </div>

        <div class="window-content">
            <div class="contact-info ubuntu-card">
                <h3>联系方式</h3>
                <div class="contact-details">
                    <div class="contact-item">
                        <strong>公司名称：</strong>北京卿逸科技中心
                    </div>
                    <div class="contact-item">
                        <strong>备案号：</strong>京ICP备2025107618号-1
                    </div>
                    <div class="contact-item">
                        <strong>成立时间：</strong>2024年
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Theme Selector Panel -->
    <div class="theme-panel" id="theme-panel">
        <div class="theme-panel-header">
            <h3>选择主题</h3>
            <button class="theme-panel-close" id="theme-panel-close">×</button>
        </div>
        <div class="theme-options">
            <div class="theme-option active" data-theme="ubuntu-default">
                <div class="theme-preview">
                    <div class="theme-bg ubuntu-default-bg"></div>
                    <div class="theme-dock ubuntu-default-dock"></div>
                    <div class="theme-window ubuntu-default-window"></div>
                </div>
                <span class="theme-name">Ubuntu 经典</span>
            </div>
            <div class="theme-option" data-theme="ubuntu-dark">
                <div class="theme-preview">
                    <div class="theme-bg ubuntu-dark-bg"></div>
                    <div class="theme-dock ubuntu-dark-dock"></div>
                    <div class="theme-window ubuntu-dark-window"></div>
                </div>
                <span class="theme-name">Ubuntu 深色</span>
            </div>
            <div class="theme-option" data-theme="ubuntu-light">
                <div class="theme-preview">
                    <div class="theme-bg ubuntu-light-bg"></div>
                    <div class="theme-dock ubuntu-light-dock"></div>
                    <div class="theme-window ubuntu-light-window"></div>
                </div>
                <span class="theme-name">Ubuntu 浅色</span>
            </div>
            <div class="theme-option" data-theme="ubuntu-purple">
                <div class="theme-preview">
                    <div class="theme-bg ubuntu-purple-bg"></div>
                    <div class="theme-dock ubuntu-purple-dock"></div>
                    <div class="theme-window ubuntu-purple-window"></div>
                </div>
                <span class="theme-name">Ubuntu 紫色</span>
            </div>
            <div class="theme-option" data-theme="ubuntu-green">
                <div class="theme-preview">
                    <div class="theme-bg ubuntu-green-bg"></div>
                    <div class="theme-dock ubuntu-green-dock"></div>
                    <div class="theme-window ubuntu-green-window"></div>
                </div>
                <span class="theme-name">Ubuntu 绿色</span>
            </div>
            <div class="theme-option" data-theme="ubuntu-blue">
                <div class="theme-preview">
                    <div class="theme-bg ubuntu-blue-bg"></div>
                    <div class="theme-dock ubuntu-blue-dock"></div>
                    <div class="theme-window ubuntu-blue-window"></div>
                </div>
                <span class="theme-name">Ubuntu 蓝色</span>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
