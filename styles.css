* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* 高科技配色方案 */
    --primary-color: #00D4FF;
    --secondary-color: #0099FF;
    --accent-color: #00FF88;
    --warning-color: #FF6B35;
    --text-primary: #E8F4FD;
    --text-secondary: #A0C4E0;
    --text-muted: #6B8DB5;
    --background: #0A0E1A;
    --surface: #1A1F2E;
    --surface-elevated: #252B3D;
    --border: #2A3441;
    --border-glow: #00D4FF40;

    /* 渐变色 */
    --gradient-primary: linear-gradient(135deg, #00D4FF 0%, #0099FF 50%, #0066FF 100%);
    --gradient-secondary: linear-gradient(135deg, #00FF88 0%, #00D4FF 100%);
    --gradient-surface: linear-gradient(145deg, #1A1F2E 0%, #252B3D 100%);
    --gradient-glow: radial-gradient(circle, rgba(0, 212, 255, 0.2) 0%, transparent 70%);

    /* 阴影 */
    --shadow-glow: 0 0 20px rgba(0, 212, 255, 0.3);
    --shadow-elevated: 0 8px 32px rgba(0, 0, 0, 0.4);
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--background);
    background-image:
        radial-gradient(circle at 20% 20%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(0, 153, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 60%, rgba(0, 255, 136, 0.05) 0%, transparent 50%);
    overflow-x: hidden;
    position: relative;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        linear-gradient(45deg, transparent 49%, rgba(0, 212, 255, 0.03) 50%, transparent 51%),
        linear-gradient(-45deg, transparent 49%, rgba(0, 153, 255, 0.03) 50%, transparent 51%);
    background-size: 20px 20px;
    pointer-events: none;
    z-index: -1;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(26, 31, 46, 0.9);
    backdrop-filter: blur(20px) saturate(180%);
    border-bottom: 1px solid var(--border-glow);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 24px;
    height: 64px;
}

/* 增强 Logo 设计 */
.logo {
    display: flex;
    align-items: center;
    gap: 16px;
    font-weight: 600;
    font-size: 18px;
    color: var(--text-primary);
    position: relative;
}

.logo-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.logo-svg {
    filter: drop-shadow(0 0 10px rgba(0, 212, 255, 0.5));
    transition: all 0.3s ease;
}

.logo:hover .logo-svg {
    filter: drop-shadow(0 0 20px rgba(0, 212, 255, 0.8));
    transform: scale(1.1);
}

.logo-particles {
    position: absolute;
    width: 60px;
    height: 60px;
    pointer-events: none;
}

.logo-particles::before,
.logo-particles::after {
    content: '';
    position: absolute;
    width: 2px;
    height: 2px;
    background: var(--accent-color);
    border-radius: 50%;
    animation: logoParticle 3s linear infinite;
}

.logo-particles::before {
    top: 10px;
    left: 10px;
    animation-delay: 0s;
}

.logo-particles::after {
    bottom: 10px;
    right: 10px;
    animation-delay: 1.5s;
}

@keyframes logoParticle {
    0% { opacity: 0; transform: scale(0); }
    50% { opacity: 1; transform: scale(1); }
    100% { opacity: 0; transform: scale(0); }
}

.logo-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
    letter-spacing: 0.5px;
    position: relative;
}

.logo-text::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: width 0.3s ease;
}

.logo:hover .logo-text::after {
    width: 100%;
}

/* 科技感导航菜单 */
.nav-menu {
    display: flex;
    list-style: none;
    gap: 8px;
}

.nav-item {
    position: relative;
}

.nav-link {
    display: block;
    padding: 12px 20px;
    text-decoration: none;
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 14px;
    border-radius: 8px;
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid transparent;
}

.nav-text {
    position: relative;
    z-index: 2;
    transition: color 0.3s ease;
}

.nav-glow {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 8px;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.2), transparent);
    transition: left 0.6s ease;
    z-index: 1;
}

.nav-link:hover {
    color: var(--text-primary);
    border-color: var(--border-glow);
    box-shadow: var(--shadow-glow);
    transform: translateY(-2px);
}

.nav-link:hover .nav-glow {
    opacity: 0.1;
}

.nav-link:hover::before {
    left: 100%;
}

.nav-link:hover .nav-text {
    color: var(--primary-color);
    text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

/* 活跃状态 */
.nav-link.active {
    color: var(--primary-color);
    border-color: var(--primary-color);
    background: rgba(0, 212, 255, 0.1);
}

.nav-link.active .nav-glow {
    opacity: 0.2;
}

/* 导航菜单动画进入效果 */
.nav-item {
    animation: navItemSlide 0.6s ease forwards;
    opacity: 0;
    transform: translateY(-20px);
}

.nav-item:nth-child(1) { animation-delay: 0.1s; }
.nav-item:nth-child(2) { animation-delay: 0.2s; }
.nav-item:nth-child(3) { animation-delay: 0.3s; }
.nav-item:nth-child(4) { animation-delay: 0.4s; }
.nav-item:nth-child(5) { animation-delay: 0.5s; }

@keyframes navItemSlide {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 导航粒子动画 */
@keyframes navParticleFloat {
    0% {
        opacity: 0;
        transform: translateY(0) scale(0);
    }
    50% {
        opacity: 1;
        transform: translateY(-10px) scale(1);
    }
    100% {
        opacity: 0;
        transform: translateY(-20px) scale(0);
    }
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding: 80px 24px 0;
    position: relative;
    overflow: hidden;
    background:
        radial-gradient(circle at 30% 20%, rgba(0, 212, 255, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 70% 80%, rgba(0, 153, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 50% 50%, rgba(0, 255, 136, 0.05) 0%, transparent 70%),
        var(--background);
}

.hero-content {
    max-width: 1200px;
    margin: 0 auto;
    text-align: center;
    z-index: 2;
}

.hero-title {
    font-size: clamp(48px, 8vw, 96px);
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: 24px;
    letter-spacing: -0.02em;
}

.gradient-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 0 20px rgba(0, 212, 255, 0.5));
}

.hero-subtitle {
    font-size: 20px;
    color: var(--text-secondary);
    margin-bottom: 48px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-buttons {
    display: flex;
    gap: 16px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-primary, .btn-secondary {
    padding: 16px 32px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none;
    display: inline-block;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--text-primary);
    border: 1px solid var(--border-glow);
    box-shadow: var(--shadow-glow);
}

.btn-primary:hover {
    transform: translateY(-4px);
    box-shadow:
        0 12px 40px rgba(0, 212, 255, 0.4),
        0 0 30px rgba(0, 212, 255, 0.6);
    filter: brightness(1.1);
}

.btn-secondary {
    background: rgba(0, 212, 255, 0.1);
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: var(--background);
    box-shadow: var(--shadow-glow);
    transform: translateY(-2px);
}

/* Floating Elements */
.hero-visual {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.floating-elements {
    position: relative;
    width: 100%;
    height: 100%;
}

.element {
    position: absolute;
    border-radius: 50%;
    background: var(--gradient-primary);
    opacity: 0.15;
    animation: float 6s ease-in-out infinite;
    filter: blur(1px);
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
}

.element-1 {
    width: 200px;
    height: 200px;
    top: 20%;
    right: 10%;
    animation-delay: 0s;
}

.element-2 {
    width: 150px;
    height: 150px;
    bottom: 30%;
    left: 15%;
    animation-delay: 2s;
}

.element-3 {
    width: 100px;
    height: 100px;
    top: 60%;
    right: 30%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

/* Products Section */
.products {
    padding: 120px 24px;
    background: var(--surface);
    background-image:
        linear-gradient(45deg, rgba(0, 212, 255, 0.02) 25%, transparent 25%),
        linear-gradient(-45deg, rgba(0, 212, 255, 0.02) 25%, transparent 25%);
    background-size: 40px 40px;
    position: relative;
}

.products::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-surface);
    opacity: 0.8;
    pointer-events: none;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
}

.section-title {
    font-size: 48px;
    font-weight: 700;
    text-align: center;
    margin-bottom: 80px;
    letter-spacing: -0.02em;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 32px;
}

.product-card {
    background: var(--surface-elevated);
    padding: 48px 32px;
    border-radius: 24px;
    text-align: center;
    border: 1px solid var(--border);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    z-index: 1;
}

.product-card:hover {
    transform: translateY(-12px);
    box-shadow:
        var(--shadow-elevated),
        var(--shadow-glow);
    border-color: var(--border-glow);
}

.product-icon {
    font-size: 48px;
    margin-bottom: 24px;
}

.product-card h3 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 16px;
}

.product-card p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 20px;
}

/* 技术统计样式 */
.tech-stats {
    display: flex;
    gap: 12px;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 16px;
}

.stat-item {
    background: linear-gradient(135deg, rgba(0, 122, 255, 0.1), rgba(88, 86, 214, 0.1));
    border: 1px solid rgba(0, 122, 255, 0.2);
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    color: var(--primary-color);
    white-space: nowrap;
    position: relative;
    overflow: hidden;
}

.stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.product-card:hover .stat-item::before {
    left: 100%;
}

/* Footer */
.footer {
    background: var(--background);
    background-image:
        linear-gradient(180deg, var(--surface) 0%, var(--background) 100%);
    color: var(--text-primary);
    padding: 80px 24px 32px;
    border-top: 1px solid var(--border);
    position: relative;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--gradient-primary);
    opacity: 0.5;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 64px;
    margin-bottom: 48px;
}

.footer-info .logo {
    color: var(--text-primary);
    margin-bottom: 16px;
}

.footer-info p {
    color: var(--text-secondary);
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 32px;
}

.link-group h4 {
    margin-bottom: 16px;
    font-weight: 600;
}

.link-group a {
    display: block;
    color: var(--text-secondary);
    text-decoration: none;
    margin-bottom: 8px;
    transition: all 0.3s ease;
    position: relative;
}

.link-group a:hover {
    color: var(--primary-color);
    text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
    transform: translateX(4px);
}

.footer-bottom {
    text-align: center;
    padding-top: 32px;
    border-top: 1px solid var(--border);
    color: var(--text-muted);
}

.footer-bottom a {
    color: var(--primary-color);
    text-decoration: none;
}

/* ==================== 科技感增强样式 ==================== */

/* 脉冲动画 */
@keyframes pulse {
    0% { transform: scale(1); box-shadow: 0 0 0 0 rgba(0, 122, 255, 0.7); }
    70% { transform: scale(1.05); box-shadow: 0 0 0 10px rgba(0, 122, 255, 0); }
    100% { transform: scale(1); box-shadow: 0 0 0 0 rgba(0, 122, 255, 0); }
}

/* 发光效果 */
@keyframes glow {
    0%, 100% { box-shadow: 0 0 5px rgba(0, 122, 255, 0.5); }
    50% { box-shadow: 0 0 20px rgba(0, 122, 255, 0.8), 0 0 30px rgba(0, 122, 255, 0.6); }
}

/* 扫描线动画 */
@keyframes scan {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100vw); }
}

/* 数据流动画 */
@keyframes dataFlow {
    0% { transform: translateX(-100%) skewX(-15deg); }
    100% { transform: translateX(100vw) skewX(-15deg); }
}

/* 全息效果 */
@keyframes hologram {
    0%, 100% { opacity: 0.8; transform: translateY(0); }
    50% { opacity: 1; transform: translateY(-2px); }
}

/* 增强的浮动动画 */
@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg) scale(1);
        opacity: 0.1;
    }
    25% {
        transform: translateY(-10px) rotate(90deg) scale(1.1);
        opacity: 0.2;
    }
    50% {
        transform: translateY(-20px) rotate(180deg) scale(1.2);
        opacity: 0.15;
    }
    75% {
        transform: translateY(-10px) rotate(270deg) scale(1.1);
        opacity: 0.2;
    }
}

/* 科技边框效果 */
.tech-border {
    position: relative;
    overflow: hidden;
}

.tech-border::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, #00ff00, transparent);
    animation: scan 3s linear infinite;
}

.tech-border::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 2px;
    height: 100%;
    background: linear-gradient(180deg, transparent, #007AFF, transparent);
    animation: dataFlow 2s linear infinite reverse;
}

/* 增强产品卡片 */
.product-card {
    position: relative;
    background: linear-gradient(145deg, var(--background) 0%, rgba(0, 122, 255, 0.02) 100%);
    border: 1px solid rgba(0, 122, 255, 0.1);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    overflow: hidden;
}

.product-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(from 0deg, transparent, rgba(0, 122, 255, 0.1), transparent);
    animation: hologram 4s linear infinite;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.product-card:hover::before {
    opacity: 1;
}

.product-card:hover {
    border-color: rgba(0, 122, 255, 0.3);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.1),
        0 0 20px rgba(0, 122, 255, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* 增强按钮效果 */
.btn-primary {
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #007AFF 0%, #5856D6 50%, #007AFF 100%);
    background-size: 200% 200%;
    animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn-primary:hover::before {
    left: 100%;
}

/* 导航栏增强 */
.navbar {
    backdrop-filter: blur(20px) saturate(180%);
    border-bottom: 1px solid rgba(0, 122, 255, 0.1);
    transition: all 0.3s ease;
}

/* 英雄区域增强 */
.hero {
    background:
        radial-gradient(circle at 20% 80%, rgba(0, 122, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(88, 86, 214, 0.1) 0%, transparent 50%),
        var(--background);
}

/* 文字发光效果 */
.gradient-text {
    position: relative;
    animation: textGlow 2s ease-in-out infinite alternate;
}

@keyframes textGlow {
    from { text-shadow: 0 0 10px rgba(0, 122, 255, 0.5); }
    to { text-shadow: 0 0 20px rgba(0, 122, 255, 0.8), 0 0 30px rgba(88, 86, 214, 0.5); }
}

/* 动画进入效果 */
.animate-in {
    animation: slideInUp 0.6s ease forwards;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .hero {
        padding: 120px 16px 0;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn-primary, .btn-secondary {
        width: 100%;
        max-width: 280px;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 32px;
    }

    .products {
        padding: 80px 16px;
    }

    /* 在移动设备上减少动画效果 */
    .product-card::before,
    .btn-primary::before {
        display: none;
    }

    .gradient-text {
        animation: none;
    }
}