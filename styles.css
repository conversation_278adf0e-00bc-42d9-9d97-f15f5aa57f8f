* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* 简洁科技配色方案 */
    --primary-color: #0066FF;
    --secondary-color: #4285F4;
    --accent-color: #00C896;
    --text-primary: #1A1A1A;
    --text-secondary: #666666;
    --text-muted: #999999;
    --background: #FFFFFF;
    --surface: #FAFAFA;
    --surface-elevated: #FFFFFF;
    --border: #E5E5E5;
    --border-light: #F0F0F0;

    /* 简洁渐变 */
    --gradient-primary: linear-gradient(135deg, #0066FF 0%, #4285F4 100%);
    --gradient-accent: linear-gradient(135deg, #00C896 0%, #0066FF 100%);

    /* 精致阴影 */
    --shadow-subtle: 0 2px 8px rgba(0, 0, 0, 0.04);
    --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.08);
    --shadow-large: 0 8px 32px rgba(0, 0, 0, 0.12);
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--background);
    overflow-x: hidden;
    position: relative;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border);
    box-shadow: var(--shadow-subtle);
    z-index: 1000;
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 24px;
    height: 64px;
}

/* 简洁 Logo 设计 */
.logo {
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 600;
    font-size: 18px;
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.logo-icon {
    transition: transform 0.3s ease;
}

.logo:hover .logo-icon {
    transform: scale(1.05);
}

.logo-text {
    font-weight: 700;
    letter-spacing: -0.02em;
    position: relative;
}

.logo:hover .logo-text {
    color: var(--primary-color);
}

/* 简洁导航菜单 */
.nav-menu {
    display: flex;
    list-style: none;
    gap: 32px;
}

.nav-item {
    position: relative;
}

.nav-link {
    display: block;
    padding: 8px 0;
    text-decoration: none;
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 15px;
    position: relative;
    transition: all 0.3s ease;
}

.nav-text {
    position: relative;
    z-index: 2;
}

.nav-glow {
    display: none; /* 简化版本不需要 */
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: width 0.3s ease;
}

.nav-link:hover {
    color: var(--primary-color);
}

.nav-link:hover::after {
    width: 100%;
}

/* 活跃状态 */
.nav-link.active {
    color: var(--primary-color);
}

.nav-link.active::after {
    width: 100%;
}

/* Main Content - Full Screen Layout */
.main-content {
    min-height: 100vh;
    display: flex;
    padding-top: 64px; /* navbar height */
    background: linear-gradient(135deg, #FAFAFA 0%, #FFFFFF 100%);
}

/* Hero Section - Left Side */
.hero-section {
    flex: 1;
    display: flex;
    align-items: center;
    padding: 40px;
    position: relative;
    overflow: hidden;
}

/* Content Section - Right Side */
.content-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: var(--surface);
    border-left: 1px solid var(--border);
}

.hero-content {
    max-width: 1200px;
    margin: 0 auto;
    text-align: center;
    z-index: 2;
}

.hero-title {
    font-size: clamp(48px, 8vw, 96px);
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: 24px;
    letter-spacing: -0.02em;
}

.gradient-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 20px;
    color: var(--text-secondary);
    margin-bottom: 48px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-buttons {
    display: flex;
    gap: 16px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-primary, .btn-secondary {
    padding: 14px 28px;
    border-radius: 8px;
    font-size: 15px;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-medium);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-large);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

/* Floating Elements */
.hero-visual {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.floating-elements {
    position: relative;
    width: 100%;
    height: 100%;
}

.element {
    position: absolute;
    border-radius: 50%;
    background: var(--gradient-primary);
    opacity: 0.08;
    animation: float 8s ease-in-out infinite;
}

.element-1 {
    width: 200px;
    height: 200px;
    top: 20%;
    right: 10%;
    animation-delay: 0s;
}

.element-2 {
    width: 150px;
    height: 150px;
    bottom: 30%;
    left: 15%;
    animation-delay: 2s;
}

.element-3 {
    width: 100px;
    height: 100px;
    top: 60%;
    right: 30%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

/* Products Section */
.products {
    padding: 120px 24px;
    background: var(--surface);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
}

.section-title {
    font-size: 48px;
    font-weight: 700;
    text-align: center;
    margin-bottom: 80px;
    letter-spacing: -0.02em;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 32px;
}

.product-card {
    background: var(--surface-elevated);
    padding: 40px 32px;
    border-radius: 16px;
    text-align: center;
    border: 1px solid var(--border);
    transition: all 0.3s ease;
    position: relative;
}

.product-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-large);
    border-color: var(--primary-color);
}

.product-icon {
    font-size: 48px;
    margin-bottom: 24px;
}

.product-card h3 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 16px;
}

.product-card p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 20px;
}

/* 技术统计样式 */
.tech-stats {
    display: flex;
    gap: 8px;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 20px;
}

.stat-item {
    background: rgba(0, 102, 255, 0.1);
    border: 1px solid rgba(0, 102, 255, 0.2);
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 500;
    color: var(--primary-color);
    white-space: nowrap;
    transition: all 0.3s ease;
}

.product-card:hover .stat-item {
    background: rgba(0, 102, 255, 0.15);
    border-color: var(--primary-color);
}

/* Solutions Section */
.solutions {
    padding: 120px 24px;
    background: var(--background);
}

.solutions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 32px;
    margin-top: 60px;
}

.solution-card {
    background: var(--surface-elevated);
    padding: 40px 32px;
    border-radius: 16px;
    border: 1px solid var(--border);
    transition: all 0.3s ease;
    position: relative;
}

.solution-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-large);
    border-color: var(--primary-color);
}

.solution-header {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 20px;
}

.solution-icon {
    font-size: 32px;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 102, 255, 0.1);
    border-radius: 12px;
    border: 1px solid rgba(0, 102, 255, 0.2);
}

.solution-card h3 {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.solution-card p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 24px;
}

.solution-features {
    list-style: none;
    padding: 0;
    margin: 0;
}

.solution-features li {
    color: var(--text-secondary);
    padding: 8px 0;
    position: relative;
    padding-left: 20px;
}

.solution-features li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--accent-color);
    font-weight: bold;
}

/* About Section */
.about {
    padding: 120px 24px;
    background: var(--surface);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
}

.about-text .section-title {
    text-align: left;
    margin-bottom: 32px;
}

.about-description {
    font-size: 18px;
    line-height: 1.7;
    color: var(--text-secondary);
    margin-bottom: 48px;
}

.about-stats {
    display: flex;
    gap: 48px;
}

.stat {
    text-align: center;
}

.stat-number {
    font-size: 36px;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 8px;
}

.stat-label {
    font-size: 14px;
    color: var(--text-secondary);
    font-weight: 500;
}

.about-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

.image-placeholder {
    width: 300px;
    height: 300px;
    background: var(--surface-elevated);
    border-radius: 16px;
    border: 1px solid var(--border);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.tech-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    padding: 40px;
}

.grid-item {
    width: 60px;
    height: 60px;
    background: rgba(0, 102, 255, 0.1);
    border: 1px solid rgba(0, 102, 255, 0.2);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.grid-item.active {
    background: rgba(0, 102, 255, 0.2);
    border-color: var(--primary-color);
    box-shadow: 0 0 20px rgba(0, 102, 255, 0.3);
}

.grid-item:hover {
    background: rgba(0, 102, 255, 0.15);
    transform: scale(1.05);
}

/* Footer */
.footer {
    background: var(--surface);
    color: var(--text-primary);
    padding: 80px 24px 32px;
    border-top: 1px solid var(--border);
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 64px;
    margin-bottom: 48px;
}

.footer-info .logo {
    color: var(--text-primary);
    margin-bottom: 16px;
}

.footer-info p {
    color: var(--text-secondary);
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 32px;
}

.link-group h4 {
    margin-bottom: 16px;
    font-weight: 600;
}

.link-group a {
    display: block;
    color: var(--text-secondary);
    text-decoration: none;
    margin-bottom: 8px;
    transition: color 0.3s ease;
}

.link-group a:hover {
    color: var(--primary-color);
}

.footer-bottom {
    text-align: center;
    padding-top: 32px;
    border-top: 1px solid var(--border);
    color: var(--text-muted);
}

.footer-bottom a {
    color: var(--primary-color);
    text-decoration: none;
}

/* ==================== 简洁动画效果 ==================== */

/* 简洁浮动动画 */
@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

/* 动画进入效果 */
.animate-in {
    animation: slideInUp 0.6s ease forwards;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .hero {
        padding: 120px 16px 0;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn-primary, .btn-secondary {
        width: 100%;
        max-width: 280px;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 32px;
    }

    .products {
        padding: 80px 16px;
    }

    .solutions {
        padding: 80px 16px;
    }

    .solutions-grid {
        grid-template-columns: 1fr;
        gap: 24px;
    }

    .about {
        padding: 80px 16px;
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: 48px;
        text-align: center;
    }

    .about-stats {
        justify-content: center;
        gap: 32px;
    }

    .image-placeholder {
        width: 250px;
        height: 250px;
    }

    .tech-grid {
        padding: 30px;
        gap: 12px;
    }

    .grid-item {
        width: 50px;
        height: 50px;
    }
}