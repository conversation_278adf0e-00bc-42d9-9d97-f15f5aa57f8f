* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* 简洁科技配色方案 */
    --primary-color: #0066FF;
    --secondary-color: #4285F4;
    --accent-color: #00C896;
    --text-primary: #1A1A1A;
    --text-secondary: #666666;
    --text-muted: #999999;
    --background: #FFFFFF;
    --surface: #FAFAFA;
    --surface-elevated: #FFFFFF;
    --border: #E5E5E5;
    --border-light: #F0F0F0;

    /* 简洁渐变 */
    --gradient-primary: linear-gradient(135deg, #0066FF 0%, #4285F4 100%);
    --gradient-accent: linear-gradient(135deg, #00C896 0%, #0066FF 100%);

    /* 精致阴影 */
    --shadow-subtle: 0 2px 8px rgba(0, 0, 0, 0.04);
    --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.08);
    --shadow-large: 0 8px 32px rgba(0, 0, 0, 0.12);
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--background);
    overflow-x: hidden;
    position: relative;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border);
    box-shadow: var(--shadow-subtle);
    z-index: 1000;
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 24px;
    height: 64px;
}

/* 简洁 Logo 设计 */
.logo {
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 600;
    font-size: 18px;
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.logo-icon {
    transition: transform 0.3s ease;
}

.logo:hover .logo-icon {
    transform: scale(1.05);
}

.logo-text {
    font-weight: 700;
    letter-spacing: -0.02em;
    position: relative;
}

.logo:hover .logo-text {
    color: var(--primary-color);
}

/* 简洁导航菜单 */
.nav-menu {
    display: flex;
    list-style: none;
    gap: 32px;
}

.nav-item {
    position: relative;
}

.nav-link {
    display: block;
    padding: 8px 0;
    text-decoration: none;
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 15px;
    position: relative;
    transition: all 0.3s ease;
    border: none;
    background: transparent;
    cursor: pointer;
    font-family: inherit;
}

.nav-text {
    position: relative;
    z-index: 2;
}

.nav-glow {
    display: none; /* 简化版本不需要 */
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: width 0.3s ease;
}

.nav-link:hover {
    color: var(--primary-color);
}

.nav-link:hover::after {
    width: 100%;
}

/* 活跃状态 */
.nav-link.active {
    color: var(--primary-color);
}

.nav-link.active::after {
    width: 100%;
}

/* Desktop Container */
.desktop-container {
    position: relative;
    width: 100%;
    height: 100vh;
    overflow: hidden;
    background: linear-gradient(135deg, #2c1810 0%, #8b4513 50%, #d2691e 100%);
}

.desktop-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.15) 0%, transparent 50%),
        linear-gradient(135deg, #2c1810 0%, #8b4513 50%, #d2691e 100%);
    background-size: 100% 100%;
    z-index: 0;
}

/* Individual Slides */
.slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    background: linear-gradient(135deg, #FAFAFA 0%, #FFFFFF 100%);
    display: flex;
    align-items: center;
    justify-content: center;
}

.slide.active {
    opacity: 1;
    transform: translateX(0);
}

.slide.prev {
    transform: translateX(-100%);
}

/* Desktop Icons */
.desktop-icons {
    position: absolute;
    top: 20px;
    left: 20px;
    z-index: 10;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
}

.desktop-icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    padding: 10px;
    border-radius: 8px;
    transition: all 0.3s ease;
    width: 80px;
    text-align: center;
}

.desktop-icon:hover {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    transform: scale(1.05);
}

.icon-image {
    font-size: 32px;
    margin-bottom: 5px;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.icon-label {
    color: white;
    font-size: 12px;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.7);
}

/* Window Styles */
.window {
    position: absolute;
    background: rgba(45, 45, 45, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.05);
    overflow: hidden;
    z-index: 5;
}

.window-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background: linear-gradient(135deg, #3a3a3a 0%, #2d2d2d 100%);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.window-controls {
    display: flex;
    gap: 8px;
}

.control {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease;
}

.control.close {
    background: #ff5f57;
}

.control.minimize {
    background: #ffbd2e;
}

.control.maximize {
    background: #28ca42;
}

.control:hover {
    transform: scale(1.1);
    box-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
}

.window-title {
    color: #ffffff;
    font-size: 14px;
    font-weight: 600;
    text-align: center;
    flex: 1;
}

.window-content {
    padding: 20px;
    color: #ffffff;
    height: calc(100% - 60px);
    overflow-y: auto;
}

/* Welcome Window */
.welcome-window {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 600px;
    height: 400px;
    z-index: 10;
}

.welcome-content {
    text-align: center;
    padding: 20px;
}

.welcome-content h1 {
    font-size: 36px;
    font-weight: 700;
    margin-bottom: 20px;
    color: #ffffff;
}

.welcome-content p {
    font-size: 16px;
    color: #cccccc;
    line-height: 1.6;
    margin-bottom: 30px;
}

.welcome-buttons {
    display: flex;
    gap: 16px;
    justify-content: center;
}

/* Product Windows */
.product-window {
    animation: windowSlideIn 0.8s ease-out;
}

.product-window-content {
    text-align: center;
}

.product-icon-window {
    font-size: 48px;
    margin-bottom: 15px;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.product-window-content h3 {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 10px;
    color: #ffffff;
}

.product-tagline {
    font-size: 14px;
    color: #00d4ff;
    margin-bottom: 20px;
    font-weight: 500;
}

.product-description {
    margin-bottom: 25px;
}

.product-description p {
    font-size: 14px;
    color: #cccccc;
    line-height: 1.5;
}

.product-stats {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    padding: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-row:last-child {
    border-bottom: none;
}

.stat-label {
    font-size: 13px;
    color: #aaaaaa;
}

.stat-value {
    font-size: 14px;
    font-weight: 600;
    color: #00d4ff;
}

/* Slide Content */
.slide-content {
    width: 100%;
    max-width: 1200px;
    padding: 0 40px;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

/* Slide Headers */
.slide-header {
    margin-bottom: 60px;
}

.slide-title {
    font-size: clamp(36px, 6vw, 64px);
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: 24px;
    letter-spacing: -0.02em;
    color: var(--text-primary);
}

.slide-subtitle {
    font-size: 20px;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* First Slide Layout */
#slide-home .slide-content {
    flex-direction: row;
    text-align: left;
    align-items: center;
}

#slide-home .slide-text {
    flex: 1;
    max-width: 600px;
}

#slide-home .slide-visual {
    flex: 1;
    position: relative;
    height: 400px;
}

/* Slide Buttons */
.slide-buttons {
    display: flex;
    gap: 16px;
    margin-top: 40px;
    justify-content: center;
}

#slide-home .slide-buttons {
    justify-content: flex-start;
}

/* Products Showcase - Large Format */
.products-showcase {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
    width: 100%;
    max-width: 1400px;
    margin-top: 60px;
}

.product-card-large {
    background: var(--surface-elevated);
    padding: 40px;
    border-radius: 20px;
    border: 1px solid var(--border);
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
    min-height: 400px;
}

.product-card-large:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-large);
    border-color: var(--primary-color);
}

.product-header {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    margin-bottom: 24px;
}

.product-icon-large {
    font-size: 48px;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gradient-primary);
    border-radius: 16px;
    color: white;
    flex-shrink: 0;
}

.product-title-section h3 {
    font-size: 24px;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 8px 0;
}

.product-tagline {
    font-size: 14px;
    color: var(--primary-color);
    font-weight: 500;
    margin: 0;
}

.product-description {
    margin-bottom: 32px;
}

.product-description p {
    font-size: 16px;
    line-height: 1.6;
    color: var(--text-secondary);
}

.product-features {
    display: flex;
    justify-content: space-between;
    gap: 16px;
}

.feature-item {
    text-align: center;
    flex: 1;
}

.feature-number {
    display: block;
    font-size: 20px;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 4px;
}

.feature-label {
    font-size: 12px;
    color: var(--text-muted);
    font-weight: 500;
}

/* Solutions Showcase - Large Format */
.solutions-grid-large {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
    width: 100%;
    max-width: 1400px;
    margin-top: 60px;
}

.solution-card-large {
    background: var(--surface-elevated);
    border-radius: 20px;
    border: 1px solid var(--border);
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
    min-height: 500px;
    display: flex;
    flex-direction: column;
}

.solution-card-large:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-large);
    border-color: var(--primary-color);
}

.solution-visual {
    position: relative;
    height: 120px;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.solution-icon-large {
    font-size: 48px;
    color: white;
    z-index: 2;
    position: relative;
}

.solution-bg-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
}

.solution-content {
    padding: 32px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.solution-content h3 {
    font-size: 22px;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 16px 0;
}

.solution-description {
    font-size: 15px;
    line-height: 1.6;
    color: var(--text-secondary);
    margin-bottom: 24px;
    flex: 1;
}

.solution-highlights {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.highlight-item {
    padding: 16px;
    background: rgba(0, 102, 255, 0.05);
    border-radius: 12px;
    border-left: 3px solid var(--primary-color);
}

.highlight-item h4 {
    font-size: 14px;
    font-weight: 600;
    color: var(--primary-color);
    margin: 0 0 4px 0;
}

.highlight-item p {
    font-size: 13px;
    color: var(--text-secondary);
    margin: 0;
    line-height: 1.4;
}

/* About Layout - Large Format */
.about-hero {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
    width: 100%;
    max-width: 1400px;
    margin-bottom: 80px;
}

.about-main {
    text-align: left;
}

.about-tagline {
    font-size: 24px;
    color: var(--primary-color);
    font-weight: 500;
    margin: 16px 0 32px 0;
    letter-spacing: 2px;
}

.about-description-large p {
    font-size: 18px;
    line-height: 1.8;
    color: var(--text-secondary);
    margin: 0;
}

.about-visual-large {
    display: flex;
    justify-content: center;
    align-items: center;
}

.company-metrics {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 32px;
    width: 100%;
    max-width: 400px;
}

.metric-circle {
    width: 160px;
    height: 160px;
    border-radius: 50%;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    transition: all 0.3s ease;
}

.metric-circle:hover {
    transform: scale(1.05);
    box-shadow: 0 10px 30px rgba(0, 102, 255, 0.3);
}

.metric-content {
    text-align: center;
    color: white;
}

.metric-number {
    display: block;
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 8px;
}

.metric-label {
    font-size: 14px;
    font-weight: 500;
    opacity: 0.9;
}

.company-values {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 48px;
    width: 100%;
    max-width: 1200px;
}

.value-item {
    text-align: center;
    padding: 32px 24px;
    background: var(--surface-elevated);
    border-radius: 16px;
    border: 1px solid var(--border);
    transition: all 0.3s ease;
}

.value-item:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-medium);
    border-color: var(--primary-color);
}

.value-icon {
    font-size: 48px;
    margin-bottom: 20px;
    display: block;
}

.value-item h3 {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 16px 0;
}

.value-item p {
    font-size: 15px;
    line-height: 1.6;
    color: var(--text-secondary);
    margin: 0;
}



.hero-content {
    max-width: 600px;
    text-align: left;
    z-index: 2;
}

.hero-title {
    font-size: clamp(36px, 6vw, 64px);
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: 24px;
    letter-spacing: -0.02em;
}

.gradient-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 20px;
    color: var(--text-secondary);
    margin-bottom: 48px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-buttons {
    display: flex;
    gap: 16px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-primary, .btn-secondary {
    padding: 14px 28px;
    border-radius: 8px;
    font-size: 15px;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-medium);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-large);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

/* Floating Elements */
.hero-visual {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.floating-elements {
    position: relative;
    width: 100%;
    height: 100%;
}

.element {
    position: absolute;
    border-radius: 50%;
    background: var(--gradient-primary);
    opacity: 0.08;
    animation: float 8s ease-in-out infinite;
}

.element-1 {
    width: 200px;
    height: 200px;
    top: 20%;
    right: 10%;
    animation-delay: 0s;
}

.element-2 {
    width: 150px;
    height: 150px;
    bottom: 30%;
    left: 15%;
    animation-delay: 2s;
}

.element-3 {
    width: 100px;
    height: 100px;
    top: 60%;
    right: 30%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

/* Products Section */
.products {
    padding: 120px 24px;
    background: var(--surface);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
}

.section-title {
    font-size: 48px;
    font-weight: 700;
    text-align: center;
    margin-bottom: 80px;
    letter-spacing: -0.02em;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 24px;
    height: 100%;
}

.product-card {
    background: var(--surface-elevated);
    padding: 40px 32px;
    border-radius: 16px;
    text-align: center;
    border: 1px solid var(--border);
    transition: all 0.3s ease;
    position: relative;
}

.product-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-large);
    border-color: var(--primary-color);
}

.product-icon {
    font-size: 48px;
    margin-bottom: 24px;
}

.product-card h3 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 16px;
}

.product-card p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 20px;
}

/* 技术统计样式 */
.tech-stats {
    display: flex;
    gap: 8px;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 20px;
}

.stat-item {
    background: rgba(0, 102, 255, 0.1);
    border: 1px solid rgba(0, 102, 255, 0.2);
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 500;
    color: var(--primary-color);
    white-space: nowrap;
    transition: all 0.3s ease;
}

.product-card:hover .stat-item {
    background: rgba(0, 102, 255, 0.15);
    border-color: var(--primary-color);
}

/* Solutions Section */
.solutions {
    padding: 120px 24px;
    background: var(--background);
}

.solutions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 24px;
    height: 100%;
}

.solution-card {
    background: var(--surface-elevated);
    padding: 40px 32px;
    border-radius: 16px;
    border: 1px solid var(--border);
    transition: all 0.3s ease;
    position: relative;
}

.solution-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-large);
    border-color: var(--primary-color);
}

.solution-header {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 20px;
}

.solution-icon {
    font-size: 32px;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 102, 255, 0.1);
    border-radius: 12px;
    border: 1px solid rgba(0, 102, 255, 0.2);
}

.solution-card h3 {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.solution-card p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 24px;
}

.solution-features {
    list-style: none;
    padding: 0;
    margin: 0;
}

.solution-features li {
    color: var(--text-secondary);
    padding: 8px 0;
    position: relative;
    padding-left: 20px;
}

.solution-features li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--accent-color);
    font-weight: bold;
}

/* About Section */
.about {
    padding: 120px 24px;
    background: var(--surface);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: center;
    height: 100%;
}

.about-text h3 {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 20px;
}

.about-description {
    font-size: 18px;
    line-height: 1.7;
    color: var(--text-secondary);
    margin-bottom: 48px;
}

.about-stats {
    display: flex;
    gap: 48px;
}

.stat {
    text-align: center;
}

.stat-number {
    font-size: 36px;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 8px;
}

.stat-label {
    font-size: 14px;
    color: var(--text-secondary);
    font-weight: 500;
}

.about-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

.image-placeholder {
    width: 300px;
    height: 300px;
    background: var(--surface-elevated);
    border-radius: 16px;
    border: 1px solid var(--border);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.tech-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    padding: 40px;
}

.grid-item {
    width: 60px;
    height: 60px;
    background: rgba(0, 102, 255, 0.1);
    border: 1px solid rgba(0, 102, 255, 0.2);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.grid-item.active {
    background: rgba(0, 102, 255, 0.2);
    border-color: var(--primary-color);
    box-shadow: 0 0 20px rgba(0, 102, 255, 0.3);
}

.grid-item:hover {
    background: rgba(0, 102, 255, 0.15);
    transform: scale(1.05);
}

/* Footer */
.footer {
    background: var(--surface);
    color: var(--text-primary);
    padding: 80px 24px 32px;
    border-top: 1px solid var(--border);
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 64px;
    margin-bottom: 48px;
}

.footer-info .logo {
    color: var(--text-primary);
    margin-bottom: 16px;
}

.footer-info p {
    color: var(--text-secondary);
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 32px;
}

.link-group h4 {
    margin-bottom: 16px;
    font-weight: 600;
}

.link-group a {
    display: block;
    color: var(--text-secondary);
    text-decoration: none;
    margin-bottom: 8px;
    transition: color 0.3s ease;
}

.link-group a:hover {
    color: var(--primary-color);
}

.footer-bottom {
    text-align: center;
    padding-top: 32px;
    border-top: 1px solid var(--border);
    color: var(--text-muted);
}

.footer-bottom a {
    color: var(--primary-color);
    text-decoration: none;
}

/* ==================== 简洁动画效果 ==================== */

/* 简洁浮动动画 */
@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

/* Window Animations */
@keyframes windowSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Terminal Window */
.terminal-window {
    top: 20%;
    left: 20%;
    width: 60%;
    height: 60%;
    z-index: 15;
    display: none;
}

.terminal-window.active {
    display: block;
    animation: windowSlideIn 0.5s ease-out;
}

.terminal-content {
    background: #1e1e1e;
    color: #00ff00;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    padding: 20px;
    height: 100%;
    overflow-y: auto;
}

.terminal-prompt {
    color: #00d4ff;
}

.terminal-output {
    margin: 10px 0;
    white-space: pre-wrap;
}

/* 动画进入效果 */
.animate-in {
    animation: slideInUp 0.6s ease forwards;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .slide-content {
        padding: 0 20px;
    }

    .slide-title {
        font-size: clamp(28px, 8vw, 48px);
    }

    .slide-subtitle {
        font-size: 16px;
    }

    /* First slide mobile layout */
    #slide-home .slide-content {
        flex-direction: column;
        text-align: center;
    }

    #slide-home .slide-visual {
        height: 200px;
        margin-top: 40px;
    }

    #slide-home .slide-buttons {
        justify-content: center;
    }

    .slide-buttons {
        flex-direction: column;
        align-items: center;
        gap: 12px;
    }

    .btn-primary, .btn-secondary {
        width: 100%;
        max-width: 280px;
    }

    /* Products mobile */
    .products-showcase {
        grid-template-columns: 1fr;
        gap: 24px;
        margin-top: 40px;
    }

    .product-card-large {
        min-height: auto;
        padding: 24px;
    }

    .product-header {
        flex-direction: column;
        text-align: center;
        gap: 16px;
    }

    .product-icon-large {
        width: 60px;
        height: 60px;
        font-size: 32px;
        margin: 0 auto;
    }

    .product-features {
        flex-direction: column;
        gap: 12px;
    }

    /* Solutions mobile */
    .solutions-grid-large {
        grid-template-columns: 1fr;
        gap: 24px;
        margin-top: 40px;
    }

    .solution-card-large {
        min-height: auto;
    }

    .solution-visual {
        height: 80px;
    }

    .solution-icon-large {
        font-size: 32px;
    }

    .solution-content {
        padding: 24px;
    }

    /* About mobile layout */
    .about-hero {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
        margin-bottom: 60px;
    }

    .about-main {
        text-align: center;
    }

    .about-tagline {
        font-size: 18px;
    }

    .about-description-large p {
        font-size: 16px;
    }

    .company-metrics {
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        max-width: 300px;
        margin: 0 auto;
    }

    .metric-circle {
        width: 120px;
        height: 120px;
    }

    .metric-number {
        font-size: 20px;
    }

    .metric-label {
        font-size: 12px;
    }

    .company-values {
        grid-template-columns: 1fr;
        gap: 24px;
    }

    .value-item {
        padding: 24px 20px;
    }

    .value-icon {
        font-size: 36px;
        margin-bottom: 16px;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 32px;
    }
}