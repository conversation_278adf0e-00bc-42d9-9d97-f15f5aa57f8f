/* Ubuntu Style CSS */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Ubuntu', sans-serif;
    background: linear-gradient(135deg, #e95420 0%, #772953 50%, #2c001e 100%);
    height: 100vh;
    overflow: hidden;
}

/* Ubuntu Desktop */
.ubuntu-desktop {
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Top Bar */
.top-bar {
    height: 32px;
    background: rgba(44, 0, 30, 0.95);
    backdrop-filter: blur(10px);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 12px;
    color: white;
    font-size: 14px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
}

.top-bar-center {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    pointer-events: none;
}

.top-bar-left {
    flex: 0 0 auto;
}

.top-bar-right {
    flex: 0 0 auto;
}

.task-manager-button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 14px;
    font-weight: 500;
    color: inherit;
}

.task-manager-button:hover {
    background: rgba(128, 128, 128, 0.1);
}

.task-manager-button.active {
    background: rgba(233, 84, 32, 0.3);
    color: #e95420;
}

.task-icon {
    display: flex;
    flex-direction: column;
    gap: 2px;
    width: 16px;
    height: 12px;
}

.task-icon-bar {
    height: 2px;
    background: currentColor;
    border-radius: 1px;
    transition: all 0.2s;
}

.task-icon-bar:nth-child(1) {
    width: 16px;
}

.task-icon-bar:nth-child(2) {
    width: 12px;
}

.task-icon-bar:nth-child(3) {
    width: 8px;
}

/* Task Manager Overview Mode */
.ubuntu-desktop.overview-mode {
    background: rgba(0, 0, 0, 0.85);
}

.ubuntu-desktop.overview-mode .desktop-content {
    background: rgba(0, 0, 0, 0.6);
}

.overview-container {
    position: fixed;
    top: 80px;
    left: 50%;
    transform: translateX(-50%);
    width: 90%;
    max-width: 1200px;
    z-index: 10000;
    display: none;
}

.overview-container.active {
    display: block;
}

.overview-header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

.overview-header h2 {
    font-size: 2rem;
    font-weight: 300;
    margin-bottom: 8px;
}

.overview-header p {
    font-size: 1rem;
    opacity: 0.8;
}

.overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 20px;
    padding: 20px;
}

.overview-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    border: 2px solid rgba(233, 84, 32, 0.3);
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    backdrop-filter: blur(10px);
}

.overview-card:hover {
    border-color: #e95420;
    box-shadow: 0 12px 40px rgba(233, 84, 32, 0.4);
    transform: translateY(-4px);
}

.overview-card-preview {
    padding: 16px;
}

.overview-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.overview-card-title {
    font-size: 16px;
    font-weight: 600;
    color: #2c001e;
    flex: 1;
}

.overview-card-status {
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 12px;
    background: rgba(233, 84, 32, 0.1);
    color: #e95420;
    font-weight: 500;
}

.overview-card-content {
    display: flex;
    align-items: center;
    gap: 16px;
}

.overview-card-icon {
    font-size: 32px;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #e95420, #ff6b35);
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(233, 84, 32, 0.3);
}

.overview-card-info {
    flex: 1;
}

.overview-card-app {
    font-size: 18px;
    font-weight: 600;
    color: #2c001e;
    margin-bottom: 4px;
}

.overview-card-desc {
    font-size: 14px;
    color: #666;
}

.clock {
    font-weight: 500;
}

.system-indicators {
    display: flex;
    gap: 12px;
    align-items: center;
}

.beian-link {
    font-size: 11px;
    color: inherit;
    opacity: 0.7;
    text-decoration: none;
    padding: 2px 6px;
    border-radius: 4px;
    transition: all 0.2s ease;
    border: 1px solid transparent;
    font-weight: 400;
    letter-spacing: 0.5px;
}

.beian-link:hover {
    opacity: 0.9;
    background: rgba(128, 128, 128, 0.1);
    border-color: rgba(128, 128, 128, 0.2);
    text-decoration: none;
}

.theme-switcher {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: inherit;
    opacity: 0.8;
}

.theme-switcher:hover {
    background: rgba(128, 128, 128, 0.1);
    opacity: 1;
}

.indicator {
    padding: 2px 6px;
    border-radius: 3px;
    cursor: pointer;
    transition: background 0.2s;
    color: inherit;
}

.indicator:hover {
    background: rgba(128, 128, 128, 0.1);
}

/* Desktop Content */
.desktop-content {
    flex: 1;
    display: flex;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
}

/* Remove Ubuntu Applications Menu styles - no longer needed */

/* Ubuntu Style Dock */
.mac-dock {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    display: flex;
    justify-content: center;
    overflow: visible;
}

.dock-container {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 8px 16px;
    background: linear-gradient(135deg,
        rgba(60, 60, 60, 0.95) 0%,
        rgba(40, 40, 40, 0.95) 50%,
        rgba(30, 30, 30, 0.95) 100%);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    min-height: 64px;
    max-width: 85%;
    overflow: visible;
    position: relative;
}

.dock-item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    padding: 6px;
    border-radius: 12px;
    min-width: 56px;
    flex-shrink: 0;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.dock-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.dock-item.active {
    background: linear-gradient(135deg,
        rgba(233, 84, 32, 0.3) 0%,
        rgba(233, 84, 32, 0.2) 100%);
    border: 1px solid rgba(233, 84, 32, 0.6);
    box-shadow: 0 4px 12px rgba(233, 84, 32, 0.3);
}

.dock-item.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 6px;
    height: 6px;
    background: linear-gradient(45deg, #e95420, #ff6b35);
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(233, 84, 32, 0.5);
}

.dock-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.05) 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.dock-icon:hover {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.15) 0%,
        rgba(255, 255, 255, 0.08) 100%);
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow:
        0 4px 16px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.dock-icon svg {
    width: 32px;
    height: 32px;
}

/* Ubuntu风格的现代图标颜色 */
.dock-item[data-view="home"] .dock-icon svg {
    color: #4CAF50; /* 绿色 - 首页 */
    filter: drop-shadow(0 2px 4px rgba(76, 175, 80, 0.3));
}

.dock-item[data-view="products"] .dock-icon svg {
    color: #2196F3; /* 蓝色 - 产品 */
    filter: drop-shadow(0 2px 4px rgba(33, 150, 243, 0.3));
}

.dock-item[data-view="solutions"] .dock-icon svg {
    color: #FF9800; /* 橙色 - 方案 */
    filter: drop-shadow(0 2px 4px rgba(255, 152, 0, 0.3));
}

.dock-item[data-view="about"] .dock-icon svg {
    color: #9C27B0; /* 紫色 - 关于 */
    filter: drop-shadow(0 2px 4px rgba(156, 39, 176, 0.3));
}

.dock-item[data-view="contact"] .dock-icon svg {
    color: #F44336; /* 红色 - 联系 */
    filter: drop-shadow(0 2px 4px rgba(244, 67, 54, 0.3));
}

.dock-item[data-app="analytics"] .dock-icon svg {
    color: #00BCD4; /* 青色 - 分析 */
    filter: drop-shadow(0 2px 4px rgba(0, 188, 212, 0.3));
}

.dock-item[data-app="cloud"] .dock-icon svg {
    color: #607D8B; /* 蓝灰色 - 云端 */
    filter: drop-shadow(0 2px 4px rgba(96, 125, 139, 0.3));
}

.dock-item[data-app="ai"] .dock-icon svg {
    color: #FFC107; /* 金色 - AI */
    filter: drop-shadow(0 2px 4px rgba(255, 193, 7, 0.3));
}

/* 悬停时的图标效果 */
.dock-item:hover .dock-icon svg {
    transform: scale(1.1);
    filter: brightness(1.2) drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

/* 活跃状态的图标效果 */
.dock-item.active .dock-icon svg {
    transform: scale(1.05);
    filter: brightness(1.3) drop-shadow(0 4px 12px currentColor);
}

.dock-label {
    position: absolute;
    bottom: 70px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg,
        rgba(40, 40, 40, 0.95) 0%,
        rgba(20, 20, 20, 0.95) 100%);
    backdrop-filter: blur(10px);
    color: white;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow:
        0 8px 24px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    z-index: 10001;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.dock-item:hover .dock-label {
    opacity: 1;
    transform: translateX(-50%) translateY(-4px);
}

.dock-separator {
    width: 2px;
    height: 32px;
    background: linear-gradient(180deg,
        transparent 0%,
        rgba(255, 255, 255, 0.3) 20%,
        rgba(255, 255, 255, 0.5) 50%,
        rgba(255, 255, 255, 0.3) 80%,
        transparent 100%);
    border-radius: 1px;
    margin: 0 8px;
    align-self: center;
    box-shadow: 0 0 4px rgba(255, 255, 255, 0.1);
}



/* Window dragging */
.app-window.dragging {
    user-select: none;
    pointer-events: none;
}

.window-header {
    cursor: move;
}

/* Window animations */
.app-window {
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.app-window.opening {
    animation: windowOpen 0.3s ease-out;
}

.app-window.closing {
    animation: windowClose 0.3s ease-in;
}

@keyframes windowOpen {
    from {
        opacity: 0;
        transform: scale(0.8) translateY(20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

@keyframes windowClose {
    from {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
    to {
        opacity: 0;
        transform: scale(0.8) translateY(20px);
    }
}

/* Main Content - Desktop Background */
.main-content {
    flex: 1;
    position: relative;
    overflow: hidden;
}

.desktop-background {
    width: 100%;
    height: 100%;
    position: relative;
}

.desktop-wallpaper {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
        rgba(233, 84, 32, 0.1) 0%,
        rgba(119, 41, 83, 0.1) 50%,
        rgba(44, 0, 30, 0.1) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.welcome-text h1 {
    font-size: 3rem;
    font-weight: 300;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 16px;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.welcome-text p {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.7);
    text-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
}

/* App Windows */
.app-window {
    position: fixed;
    width: 800px;
    height: 600px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
    z-index: 10;
    display: none;
    resize: both;
    min-width: 400px;
    min-height: 300px;
    max-width: 90vw;
    max-height: 90vh;
}

.app-window.active {
    display: flex;
    flex-direction: column;
}

.app-window.minimized {
    display: none;
}

.app-window.maximized {
    width: 100vw !important;
    height: calc(100vh - 32px) !important;
    top: 32px !important;
    left: 0 !important;
    border-radius: 0;
    resize: none;
}

/* Window positioning */
.app-window[data-app="home"] {
    top: 80px;
    left: 100px;
}

.app-window[data-app="products"] {
    top: 120px;
    left: 140px;
}

.app-window[data-app="solutions"] {
    top: 160px;
    left: 180px;
}

.app-window[data-app="about"] {
    top: 200px;
    left: 220px;
}

.app-window[data-app="contact"] {
    top: 240px;
    left: 260px;
}

/* Ubuntu Window Header */
.ubuntu-header {
    height: 40px;
    background: linear-gradient(135deg, #3c3c3c, #2c2c2c);
    border-bottom: 1px solid #1a1a1a;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    color: white;
}

.window-title {
    font-weight: 500;
    font-size: 14px;
    color: white;
    flex: 1;
    text-align: center;
    margin: 0 60px; /* Space for controls */
}

.ubuntu-controls {
    display: flex;
    gap: 8px;
}

.ubuntu-control {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    pointer-events: auto;
    z-index: 10;
    position: relative;
    border: 1px solid rgba(0, 0, 0, 0.15);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    font-size: 10px;
    font-weight: bold;
    color: rgba(0, 0, 0, 0.7);
}

.ubuntu-control.close {
    background: linear-gradient(135deg, #ff6b5b 0%, #ff5a4a 100%);
}

.ubuntu-control.minimize {
    background: linear-gradient(135deg, #ffca3a 0%, #ffb700 100%);
}

.ubuntu-control.maximize {
    background: linear-gradient(135deg, #28ca42 0%, #20a934 100%);
}

.ubuntu-control:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.ubuntu-control.close:hover {
    background: linear-gradient(135deg, #ff5a4a 0%, #ff4939 100%);
}

.ubuntu-control.minimize:hover {
    background: linear-gradient(135deg, #ffb700 0%, #ff9500 100%);
}

.ubuntu-control.maximize:hover {
    background: linear-gradient(135deg, #20a934 0%, #1a8f2a 100%);
}

.ubuntu-control svg {
    width: 16px;
    height: 16px;
    pointer-events: none;
}

/* Legacy control styles - remove */
.control {
    display: none;
}

/* Window Content */
.window-content {
    padding: 24px;
    height: calc(100vh - 120px);
    overflow-y: auto;
}

/* Ubuntu Cards */
.ubuntu-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 0, 0, 0.05);
    margin-bottom: 16px;
}

/* Hero Section */
.hero-section {
    display: flex;
    align-items: center;
    gap: 40px;
    min-height: 400px;
}

.hero-text {
    flex: 1;
}

.hero-text h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c001e;
    margin-bottom: 8px;
}

.hero-text h2 {
    font-size: 1.5rem;
    font-weight: 400;
    color: #e95420;
    margin-bottom: 16px;
}

.hero-text p {
    font-size: 1.1rem;
    color: #666;
    line-height: 1.6;
    margin-bottom: 24px;
}

.action-buttons {
    display: flex;
    gap: 12px;
}

.ubuntu-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-family: 'Ubuntu', sans-serif;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.ubuntu-btn.primary {
    background: #e95420;
    color: white;
}

.ubuntu-btn.primary:hover {
    background: #d44815;
    transform: translateY(-1px);
}

.ubuntu-btn.secondary {
    background: transparent;
    color: #e95420;
    border: 2px solid #e95420;
}

.ubuntu-btn.secondary:hover {
    background: #e95420;
    color: white;
}

/* Hero Visual */
.hero-visual {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
}

.ubuntu-logo-large {
    width: 200px;
    height: 200px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.logo-ring {
    position: absolute;
    width: 100%;
    height: 100%;
    border: 4px solid #e95420;
    border-radius: 50%;
    border-top-color: transparent;
    border-right-color: transparent;
    animation: rotate 10s linear infinite;
}

.logo-center {
    font-size: 3rem;
    font-weight: 700;
    color: #e95420;
    z-index: 1;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Products Grid */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.product-card {
    transition: transform 0.2s;
}

.product-card:hover {
    transform: translateY(-4px);
}

.card-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
}

.product-icon {
    font-size: 24px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #e95420, #ff6b35);
    border-radius: 8px;
}

.card-header h3 {
    color: #2c001e;
    font-weight: 600;
}

.card-content p {
    color: #666;
    line-height: 1.5;
    margin-bottom: 12px;
}

.features {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.feature-tag {
    background: rgba(233, 84, 32, 0.1);
    color: #e95420;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

/* Solutions */
.solutions-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.solution-item {
    transition: transform 0.2s;
}

.solution-item:hover {
    transform: translateX(4px);
}

.solution-header {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    margin-bottom: 12px;
}

.solution-icon {
    font-size: 24px;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #e95420, #ff6b35);
    border-radius: 8px;
    flex-shrink: 0;
}

.solution-info h3 {
    color: #2c001e;
    font-weight: 600;
    margin-bottom: 4px;
}

.solution-info p {
    color: #666;
    line-height: 1.5;
}

.solution-details ul {
    list-style: none;
    padding-left: 64px;
}

.solution-details li {
    color: #666;
    padding: 4px 0;
    position: relative;
}

.solution-details li::before {
    content: "•";
    color: #e95420;
    position: absolute;
    left: -16px;
}

/* About */
.about-content {
    max-width: 800px;
}

.company-info h3 {
    color: #2c001e;
    font-weight: 600;
    margin-bottom: 16px;
}

.company-info p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 24px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
}

.stat-item {
    text-align: center;
    padding: 20px;
    background: rgba(233, 84, 32, 0.05);
    border-radius: 8px;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #e95420;
    margin-bottom: 4px;
}

.stat-label {
    color: #666;
    font-size: 14px;
}

/* Contact */
.contact-info h3 {
    color: #2c001e;
    font-weight: 600;
    margin-bottom: 16px;
}

.contact-details {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.contact-item {
    color: #666;
    line-height: 1.5;
}

.contact-item strong {
    color: #2c001e;
}

/* Responsive */
@media (max-width: 768px) {
    .ubuntu-menu {
        width: calc(100vw - 40px);
        left: 20px;
        right: 20px;
    }

    .app-grid {
        grid-template-columns: repeat(4, 1fr);
    }

    .dock-container {
        padding: 6px 8px;
        gap: 1px;
        min-height: 60px;
    }

    .dock-icon {
        width: 40px;
        height: 40px;
    }

    .dock-icon svg {
        width: 24px;
        height: 24px;
    }

    .dock-item {
        min-width: 44px;
        padding: 4px;
    }

    .dock-item:hover {
        /* 移动端完全无悬停效果 */
    }

    .dock-label {
        bottom: 70px;
        font-size: 12px;
        padding: 6px 10px;
    }

    .hero-section {
        flex-direction: column;
        text-align: center;
        padding: 20px 0;
    }

    .hero-text h1 {
        font-size: 2rem;
    }

    .hero-text h2 {
        font-size: 1.2rem;
    }

    .products-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .main-content {
        padding: 15px;
        margin-bottom: 120px;
    }

    .window-content {
        padding: 16px;
    }
}

@media (max-width: 480px) {
    .top-bar {
        padding: 0 8px;
    }

    .ubuntu-menu {
        width: calc(100vw - 20px);
        left: 10px;
        right: 10px;
        height: calc(100vh - 80px);
    }

    .app-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 12px;
    }

    .dock-container {
        flex-wrap: wrap;
        justify-content: center;
        max-width: calc(100vw - 40px);
    }

    .dock-separator {
        display: none;
    }
}

/* Theme System */
.theme-panel {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.9);
    width: 600px;
    max-width: 90vw;
    background: linear-gradient(135deg,
        rgba(40, 40, 40, 0.98) 0%,
        rgba(20, 20, 20, 0.98) 100%);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.theme-panel.active {
    opacity: 1;
    visibility: visible;
    transform: translate(-50%, -50%) scale(1);
}

.theme-panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.theme-panel-header h3 {
    margin: 0;
    color: white;
    font-size: 18px;
    font-weight: 500;
}

.theme-panel-close {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    font-size: 24px;
    cursor: pointer;
    width: 32px;
    height: 32px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.theme-panel-close:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.theme-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: 16px;
    padding: 24px;
}

.theme-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    padding: 16px;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 2px solid transparent;
}

.theme-option:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);
}

.theme-option.active {
    background: rgba(233, 84, 32, 0.1);
    border-color: rgba(233, 84, 32, 0.5);
}

.theme-preview {
    position: relative;
    width: 120px;
    height: 80px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.theme-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.theme-dock {
    position: absolute;
    bottom: 4px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 12px;
    border-radius: 6px;
}

.theme-window {
    position: absolute;
    top: 8px;
    left: 8px;
    width: 40px;
    height: 30px;
    border-radius: 4px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.theme-name {
    color: white;
    font-size: 14px;
    font-weight: 500;
    text-align: center;
}

/* Theme Previews */
.ubuntu-default-bg {
    background: linear-gradient(135deg, #e95420 0%, #772953 50%, #2c001e 100%);
}

.ubuntu-default-dock {
    background: linear-gradient(135deg, rgba(60, 60, 60, 0.95), rgba(30, 30, 30, 0.95));
}

.ubuntu-default-window {
    background: rgba(255, 255, 255, 0.95);
}

.ubuntu-dark-bg {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #0f0f0f 100%);
}

.ubuntu-dark-dock {
    background: linear-gradient(135deg, rgba(20, 20, 20, 0.95), rgba(10, 10, 10, 0.95));
}

.ubuntu-dark-window {
    background: rgba(40, 40, 40, 0.95);
}

.ubuntu-light-bg {
    background: linear-gradient(135deg, #fafafa 0%, #f0f0f0 50%, #e8e8e8 100%);
}

.ubuntu-light-dock {
    background: linear-gradient(135deg, rgba(248, 248, 248, 0.98), rgba(235, 235, 235, 0.98));
}

.ubuntu-light-window {
    background: rgba(255, 255, 255, 0.98);
}

.ubuntu-purple-bg {
    background: linear-gradient(135deg, #6a1b9a 0%, #4a148c 50%, #2e0854 100%);
}

.ubuntu-purple-dock {
    background: linear-gradient(135deg, rgba(106, 27, 154, 0.95), rgba(46, 8, 84, 0.95));
}

.ubuntu-purple-window {
    background: rgba(255, 255, 255, 0.95);
}

.ubuntu-green-bg {
    background: linear-gradient(135deg, #2e7d32 0%, #1b5e20 50%, #0d3f14 100%);
}

.ubuntu-green-dock {
    background: linear-gradient(135deg, rgba(46, 125, 50, 0.95), rgba(13, 63, 20, 0.95));
}

.ubuntu-green-window {
    background: rgba(255, 255, 255, 0.95);
}

.ubuntu-blue-bg {
    background: linear-gradient(135deg, #1976d2 0%, #0d47a1 50%, #0a2e5c 100%);
}

.ubuntu-blue-dock {
    background: linear-gradient(135deg, rgba(25, 118, 210, 0.95), rgba(10, 46, 92, 0.95));
}

.ubuntu-blue-window {
    background: rgba(255, 255, 255, 0.95);
}

/* Modal Styles */
.wechat-modal, .music-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(5px);
    z-index: 10000;
    display: none;
    align-items: center;
    justify-content: center;
}

.wechat-modal.active, .music-modal.active {
    display: flex;
}

.wechat-modal-content, .music-modal-content {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.98) 0%,
        rgba(248, 248, 248, 0.98) 100%);
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
    min-width: 320px;
    max-width: 90vw;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.wechat-modal-header, .music-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    background: linear-gradient(135deg, #f8f8f8, #f0f0f0);
}

.wechat-modal-header h3, .music-modal-header h3 {
    margin: 0;
    color: #2c2c2c;
    font-size: 18px;
    font-weight: 600;
}

.wechat-modal-close, .music-modal-close {
    background: none;
    border: none;
    color: #666;
    font-size: 24px;
    cursor: pointer;
    width: 32px;
    height: 32px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.wechat-modal-close:hover, .music-modal-close:hover {
    background: rgba(0, 0, 0, 0.1);
    color: #333;
}

.wechat-modal-body, .music-modal-body {
    padding: 24px;
}

/* WeChat QR Code Styles */
.qr-code-container {
    text-align: center;
}

.qr-code-placeholder {
    display: inline-block;
    padding: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    margin-bottom: 16px;
}

.qr-code-text {
    font-size: 16px;
    color: #333;
    margin: 16px 0 8px 0;
    font-weight: 500;
}

.qr-code-note {
    font-size: 14px;
    color: #666;
    margin: 0;
}

/* Music Player Styles */
.music-modal-content {
    min-width: 600px;
    max-width: 800px;
}

.music-modal-body {
    display: flex;
    gap: 24px;
}

.music-player {
    flex: 1;
    min-width: 300px;
}

.music-info {
    margin-bottom: 20px;
    text-align: center;
}

.music-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.music-artist {
    font-size: 14px;
    color: #666;
}

.music-controls {
    display: flex;
    justify-content: center;
    gap: 12px;
    margin-bottom: 20px;
}

.music-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background: linear-gradient(135deg, #e95420, #ff6b35);
    color: white;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(233, 84, 32, 0.3);
}

.music-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(233, 84, 32, 0.4);
}

.music-btn.play-pause {
    width: 48px;
    height: 48px;
    font-size: 16px;
}

.music-progress {
    margin-bottom: 16px;
}

.progress-bar {
    width: 100%;
    height: 4px;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 2px;
    margin-bottom: 8px;
    overflow: hidden;
    cursor: pointer;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #e95420, #ff6b35);
    border-radius: 2px;
    width: 35%;
    transition: width 0.3s ease;
}

.time-display {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #666;
}

.volume-control {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 16px;
}

.volume-slider {
    flex: 1;
    height: 4px;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 2px;
    outline: none;
    -webkit-appearance: none;
}

.volume-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: linear-gradient(135deg, #e95420, #ff6b35);
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.volume-value {
    font-size: 12px;
    color: #666;
    min-width: 35px;
}

.music-playlist {
    flex: 1;
    min-width: 250px;
    border-left: 1px solid rgba(0, 0, 0, 0.1);
    padding-left: 24px;
}

.playlist-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.playlist-header h4 {
    margin: 0;
    color: #333;
    font-size: 16px;
}

.playlist-add-btn {
    background: linear-gradient(135deg, #e95420, #ff6b35);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.playlist-add-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(233, 84, 32, 0.3);
}

.playlist-items {
    max-height: 300px;
    overflow-y: auto;
}

.playlist-item {
    display: flex;
    align-items: center;
    padding: 8px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-bottom: 4px;
}

.playlist-item:hover {
    background: rgba(233, 84, 32, 0.1);
}

.playlist-item.active {
    background: rgba(233, 84, 32, 0.2);
    border-left: 3px solid #e95420;
}

.playlist-item-info {
    flex: 1;
}

.playlist-item-title {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 2px;
}

.playlist-item-artist {
    font-size: 12px;
    color: #666;
}

.playlist-item-duration {
    font-size: 12px;
    color: #999;
}

.playlist-item-remove {
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.playlist-item-remove:hover {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

/* Status Menu Styles */
.status-menu {
    position: fixed;
    background: linear-gradient(135deg,
        rgba(248, 248, 248, 0.98) 0%,
        rgba(240, 240, 240, 0.98) 100%);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    min-width: 180px;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.2s ease;
    overflow: hidden;
}

.status-menu.active {
    opacity: 1;
    transform: translateY(0);
}

.status-menu-header {
    padding: 12px 16px;
    background: linear-gradient(135deg, #e95420, #ff6b35);
    color: white;
    font-weight: 600;
    font-size: 14px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.status-menu-item {
    padding: 12px 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #333;
    font-size: 14px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.status-menu-item:last-child {
    border-bottom: none;
}

.status-menu-item:hover {
    background: rgba(233, 84, 32, 0.1);
    color: #e95420;
}

/* Click outside to close */
.status-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
}

/* Notes Modal Styles */
.notes-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(5px);
    z-index: 10000;
    display: none;
    align-items: center;
    justify-content: center;
}

.notes-modal.active {
    display: flex;
}

.notes-modal-content {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.98) 0%,
        rgba(248, 248, 248, 0.98) 100%);
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
    width: 90vw;
    max-width: 900px;
    height: 80vh;
    max-height: 600px;
    animation: modalSlideIn 0.3s ease-out;
    display: flex;
    flex-direction: column;
}

.notes-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    background: linear-gradient(135deg, #f8f8f8, #f0f0f0);
}

.notes-modal-body {
    flex: 1;
    padding: 20px 24px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.notes-toolbar {
    display: flex;
    gap: 12px;
    margin-bottom: 20px;
    align-items: center;
}

.notes-btn {
    background: linear-gradient(135deg, #e95420, #ff6b35);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.notes-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(233, 84, 32, 0.3);
}

.notes-btn.cancel-btn {
    background: #6c757d;
}

.notes-btn.delete-btn {
    background: #dc3545;
}

.notes-filter {
    padding: 8px 12px;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 6px;
    background: white;
    font-size: 14px;
    outline: none;
}

.notes-container {
    flex: 1;
    display: flex;
    gap: 20px;
    overflow: hidden;
}

.notes-list {
    flex: 1;
    overflow-y: auto;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    padding: 12px;
    background: white;
}

.note-item {
    padding: 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-bottom: 8px;
    border-left: 4px solid transparent;
}

.note-item:hover {
    background: rgba(233, 84, 32, 0.05);
    border-left-color: #e95420;
}

.note-item.active {
    background: rgba(233, 84, 32, 0.1);
    border-left-color: #e95420;
}

.note-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.note-item-title {
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.note-item-category {
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 4px;
    background: rgba(233, 84, 32, 0.1);
    color: #e95420;
}

.note-item-content {
    font-size: 12px;
    color: #666;
    line-height: 1.4;
    margin-bottom: 4px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.note-item-date {
    font-size: 11px;
    color: #999;
}

.note-editor {
    flex: 1;
    display: flex;
    flex-direction: column;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    background: white;
}

.editor-header {
    display: flex;
    gap: 12px;
    padding: 16px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.note-title-input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 6px;
    font-size: 16px;
    font-weight: 600;
    outline: none;
}

.note-title-input:focus {
    border-color: #e95420;
    box-shadow: 0 0 0 2px rgba(233, 84, 32, 0.2);
}

.note-category {
    padding: 8px 12px;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 6px;
    background: white;
    font-size: 14px;
    outline: none;
}

.note-content-input {
    flex: 1;
    padding: 16px;
    border: none;
    outline: none;
    font-size: 14px;
    line-height: 1.6;
    resize: none;
    font-family: inherit;
}

.editor-footer {
    display: flex;
    gap: 8px;
    padding: 16px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    justify-content: flex-end;
}
