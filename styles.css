* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #007AFF;
    --secondary-color: #5856D6;
    --text-primary: #1D1D1F;
    --text-secondary: #86868B;
    --background: #FBFBFD;
    --surface: #FFFFFF;
    --border: #D2D2D7;
    --gradient: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--background);
    overflow-x: hidden;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border);
    z-index: 1000;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 24px;
    height: 64px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 600;
    font-size: 18px;
    color: var(--text-primary);
}

.logo svg {
    color: var(--primary-color);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 32px;
}

.nav-menu a {
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 400;
    transition: color 0.3s ease;
}

.nav-menu a:hover {
    color: var(--primary-color);
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding: 80px 24px 0;
    position: relative;
    overflow: hidden;
}

.hero-content {
    max-width: 1200px;
    margin: 0 auto;
    text-align: center;
    z-index: 2;
}

.hero-title {
    font-size: clamp(48px, 8vw, 96px);
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: 24px;
    letter-spacing: -0.02em;
}

.gradient-text {
    background: var(--gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 20px;
    color: var(--text-secondary);
    margin-bottom: 48px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-buttons {
    display: flex;
    gap: 16px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-primary, .btn-secondary {
    padding: 16px 32px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background: var(--gradient);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 32px rgba(0, 122, 255, 0.3);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: white;
}

/* Floating Elements */
.hero-visual {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.floating-elements {
    position: relative;
    width: 100%;
    height: 100%;
}

.element {
    position: absolute;
    border-radius: 50%;
    background: var(--gradient);
    opacity: 0.1;
    animation: float 6s ease-in-out infinite;
}

.element-1 {
    width: 200px;
    height: 200px;
    top: 20%;
    right: 10%;
    animation-delay: 0s;
}

.element-2 {
    width: 150px;
    height: 150px;
    bottom: 30%;
    left: 15%;
    animation-delay: 2s;
}

.element-3 {
    width: 100px;
    height: 100px;
    top: 60%;
    right: 30%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

/* Products Section */
.products {
    padding: 120px 24px;
    background: var(--surface);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
}

.section-title {
    font-size: 48px;
    font-weight: 700;
    text-align: center;
    margin-bottom: 80px;
    letter-spacing: -0.02em;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 32px;
}

.product-card {
    background: var(--background);
    padding: 48px 32px;
    border-radius: 24px;
    text-align: center;
    border: 1px solid var(--border);
    transition: all 0.3s ease;
}

.product-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.product-icon {
    font-size: 48px;
    margin-bottom: 24px;
}

.product-card h3 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 16px;
}

.product-card p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 20px;
}

/* 技术统计样式 */
.tech-stats {
    display: flex;
    gap: 12px;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 16px;
}

.stat-item {
    background: linear-gradient(135deg, rgba(0, 122, 255, 0.1), rgba(88, 86, 214, 0.1));
    border: 1px solid rgba(0, 122, 255, 0.2);
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    color: var(--primary-color);
    white-space: nowrap;
    position: relative;
    overflow: hidden;
}

.stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.product-card:hover .stat-item::before {
    left: 100%;
}

/* Footer */
.footer {
    background: var(--text-primary);
    color: white;
    padding: 80px 24px 32px;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 64px;
    margin-bottom: 48px;
}

.footer-info .logo {
    color: white;
    margin-bottom: 16px;
}

.footer-info p {
    color: #86868B;
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 32px;
}

.link-group h4 {
    margin-bottom: 16px;
    font-weight: 600;
}

.link-group a {
    display: block;
    color: #86868B;
    text-decoration: none;
    margin-bottom: 8px;
    transition: color 0.3s ease;
}

.link-group a:hover {
    color: white;
}

.footer-bottom {
    text-align: center;
    padding-top: 32px;
    border-top: 1px solid #333;
    color: #86868B;
}

.footer-bottom a {
    color: var(--primary-color);
    text-decoration: none;
}

/* ==================== 科技感增强样式 ==================== */

/* 脉冲动画 */
@keyframes pulse {
    0% { transform: scale(1); box-shadow: 0 0 0 0 rgba(0, 122, 255, 0.7); }
    70% { transform: scale(1.05); box-shadow: 0 0 0 10px rgba(0, 122, 255, 0); }
    100% { transform: scale(1); box-shadow: 0 0 0 0 rgba(0, 122, 255, 0); }
}

/* 发光效果 */
@keyframes glow {
    0%, 100% { box-shadow: 0 0 5px rgba(0, 122, 255, 0.5); }
    50% { box-shadow: 0 0 20px rgba(0, 122, 255, 0.8), 0 0 30px rgba(0, 122, 255, 0.6); }
}

/* 扫描线动画 */
@keyframes scan {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100vw); }
}

/* 数据流动画 */
@keyframes dataFlow {
    0% { transform: translateX(-100%) skewX(-15deg); }
    100% { transform: translateX(100vw) skewX(-15deg); }
}

/* 全息效果 */
@keyframes hologram {
    0%, 100% { opacity: 0.8; transform: translateY(0); }
    50% { opacity: 1; transform: translateY(-2px); }
}

/* 增强的浮动动画 */
@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg) scale(1);
        opacity: 0.1;
    }
    25% {
        transform: translateY(-10px) rotate(90deg) scale(1.1);
        opacity: 0.2;
    }
    50% {
        transform: translateY(-20px) rotate(180deg) scale(1.2);
        opacity: 0.15;
    }
    75% {
        transform: translateY(-10px) rotate(270deg) scale(1.1);
        opacity: 0.2;
    }
}

/* 科技边框效果 */
.tech-border {
    position: relative;
    overflow: hidden;
}

.tech-border::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, #00ff00, transparent);
    animation: scan 3s linear infinite;
}

.tech-border::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 2px;
    height: 100%;
    background: linear-gradient(180deg, transparent, #007AFF, transparent);
    animation: dataFlow 2s linear infinite reverse;
}

/* 增强产品卡片 */
.product-card {
    position: relative;
    background: linear-gradient(145deg, var(--background) 0%, rgba(0, 122, 255, 0.02) 100%);
    border: 1px solid rgba(0, 122, 255, 0.1);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    overflow: hidden;
}

.product-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(from 0deg, transparent, rgba(0, 122, 255, 0.1), transparent);
    animation: hologram 4s linear infinite;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.product-card:hover::before {
    opacity: 1;
}

.product-card:hover {
    border-color: rgba(0, 122, 255, 0.3);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.1),
        0 0 20px rgba(0, 122, 255, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* 增强按钮效果 */
.btn-primary {
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #007AFF 0%, #5856D6 50%, #007AFF 100%);
    background-size: 200% 200%;
    animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn-primary:hover::before {
    left: 100%;
}

/* 导航栏增强 */
.navbar {
    backdrop-filter: blur(20px) saturate(180%);
    border-bottom: 1px solid rgba(0, 122, 255, 0.1);
    transition: all 0.3s ease;
}

/* 英雄区域增强 */
.hero {
    background:
        radial-gradient(circle at 20% 80%, rgba(0, 122, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(88, 86, 214, 0.1) 0%, transparent 50%),
        var(--background);
}

/* 文字发光效果 */
.gradient-text {
    position: relative;
    animation: textGlow 2s ease-in-out infinite alternate;
}

@keyframes textGlow {
    from { text-shadow: 0 0 10px rgba(0, 122, 255, 0.5); }
    to { text-shadow: 0 0 20px rgba(0, 122, 255, 0.8), 0 0 30px rgba(88, 86, 214, 0.5); }
}

/* 动画进入效果 */
.animate-in {
    animation: slideInUp 0.6s ease forwards;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .hero {
        padding: 120px 16px 0;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn-primary, .btn-secondary {
        width: 100%;
        max-width: 280px;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 32px;
    }

    .products {
        padding: 80px 16px;
    }

    /* 在移动设备上减少动画效果 */
    .product-card::before,
    .btn-primary::before {
        display: none;
    }

    .gradient-text {
        animation: none;
    }
}