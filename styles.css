* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #007AFF;
    --secondary-color: #5856D6;
    --text-primary: #1D1D1F;
    --text-secondary: #86868B;
    --background: #FBFBFD;
    --surface: #FFFFFF;
    --border: #D2D2D7;
    --gradient: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--background);
    overflow-x: hidden;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border);
    z-index: 1000;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 24px;
    height: 64px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 600;
    font-size: 18px;
    color: var(--text-primary);
}

.logo svg {
    color: var(--primary-color);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 32px;
}

.nav-menu a {
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 400;
    transition: color 0.3s ease;
}

.nav-menu a:hover {
    color: var(--primary-color);
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding: 80px 24px 0;
    position: relative;
    overflow: hidden;
}

.hero-content {
    max-width: 1200px;
    margin: 0 auto;
    text-align: center;
    z-index: 2;
}

.hero-title {
    font-size: clamp(48px, 8vw, 96px);
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: 24px;
    letter-spacing: -0.02em;
}

.gradient-text {
    background: var(--gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 20px;
    color: var(--text-secondary);
    margin-bottom: 48px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-buttons {
    display: flex;
    gap: 16px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-primary, .btn-secondary {
    padding: 16px 32px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background: var(--gradient);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 32px rgba(0, 122, 255, 0.3);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: white;
}

/* Floating Elements */
.hero-visual {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.floating-elements {
    position: relative;
    width: 100%;
    height: 100%;
}

.element {
    position: absolute;
    border-radius: 50%;
    background: var(--gradient);
    opacity: 0.1;
    animation: float 6s ease-in-out infinite;
}

.element-1 {
    width: 200px;
    height: 200px;
    top: 20%;
    right: 10%;
    animation-delay: 0s;
}

.element-2 {
    width: 150px;
    height: 150px;
    bottom: 30%;
    left: 15%;
    animation-delay: 2s;
}

.element-3 {
    width: 100px;
    height: 100px;
    top: 60%;
    right: 30%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

/* Products Section */
.products {
    padding: 120px 24px;
    background: var(--surface);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
}

.section-title {
    font-size: 48px;
    font-weight: 700;
    text-align: center;
    margin-bottom: 80px;
    letter-spacing: -0.02em;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 32px;
}

.product-card {
    background: var(--background);
    padding: 48px 32px;
    border-radius: 24px;
    text-align: center;
    border: 1px solid var(--border);
    transition: all 0.3s ease;
}

.product-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.product-icon {
    font-size: 48px;
    margin-bottom: 24px;
}

.product-card h3 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 16px;
}

.product-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Footer */
.footer {
    background: var(--text-primary);
    color: white;
    padding: 80px 24px 32px;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 64px;
    margin-bottom: 48px;
}

.footer-info .logo {
    color: white;
    margin-bottom: 16px;
}

.footer-info p {
    color: #86868B;
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 32px;
}

.link-group h4 {
    margin-bottom: 16px;
    font-weight: 600;
}

.link-group a {
    display: block;
    color: #86868B;
    text-decoration: none;
    margin-bottom: 8px;
    transition: color 0.3s ease;
}

.link-group a:hover {
    color: white;
}

.footer-bottom {
    text-align: center;
    padding-top: 32px;
    border-top: 1px solid #333;
    color: #86868B;
}

.footer-bottom a {
    color: var(--primary-color);
    text-decoration: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }
    
    .hero {
        padding: 120px 16px 0;
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .btn-primary, .btn-secondary {
        width: 100%;
        max-width: 280px;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: 32px;
    }
    
    .products {
        padding: 80px 16px;
    }
}