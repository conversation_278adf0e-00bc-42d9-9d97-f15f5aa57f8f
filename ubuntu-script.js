// Ubuntu Style JavaScript
console.log('Ubuntu script loading...');

document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing Ubuntu interface...');

    // Window management system
    let activeWindows = new Set();
    let windowZIndex = 100;
    let draggedWindow = null;
    let dragOffset = { x: 0, y: 0 };

    // Navigation functionality for both Ubuntu menu and Mac dock
    const navItems = document.querySelectorAll('[data-view]');

    // Handle navigation clicks - open windows
    navItems.forEach(item => {
        item.addEventListener('click', function() {
            const targetApp = this.getAttribute('data-view');
            openWindow(targetApp);
        });
    });

    // Handle action buttons that open other windows
    document.addEventListener('click', function(e) {
        if (e.target.hasAttribute('data-open')) {
            const targetApp = e.target.getAttribute('data-open');
            openWindow(targetApp);
        }
    });

    // Open window function
    function openWindow(appName) {
        console.log('Opening window for app:', appName);
        const window = document.getElementById(appName + '-window');
        if (!window) {
            console.error('Window not found:', appName + '-window');
            return;
        }

        console.log('Window found:', window);

        // If window is already open, just bring it to front
        if (activeWindows.has(appName)) {
            console.log('Window already open, bringing to front');
            bringToFront(window);
            return;
        }

        // Show window
        console.log('Showing window');
        window.classList.add('active', 'opening');
        window.style.zIndex = ++windowZIndex;
        activeWindows.add(appName);

        // Add to taskbar
        addToTaskbar(appName, window);

        // Remove opening animation class
        setTimeout(() => {
            window.classList.remove('opening');
        }, 300);

        // Update dock active state
        updateDockActiveState();
    }

    // Close window function
    function closeWindow(appName) {
        const window = document.getElementById(appName + '-window');
        if (!window) return;

        window.classList.add('closing');

        setTimeout(() => {
            window.classList.remove('active', 'closing');
            activeWindows.delete(appName);
            removeFromTaskbar(appName);
            updateDockActiveState();
        }, 300);
    }

    // Minimize window function
    function minimizeWindow(appName) {
        const window = document.getElementById(appName + '-window');
        if (!window) return;

        window.classList.add('minimized');
        window.classList.remove('active');

        // Update taskbar item
        const taskbarItem = document.querySelector(`[data-taskbar-app="${appName}"]`);
        if (taskbarItem) {
            taskbarItem.classList.remove('active');
        }
    }

    // Maximize/restore window function
    function toggleMaximize(appName) {
        const window = document.getElementById(appName + '-window');
        if (!window) return;

        window.classList.toggle('maximized');
    }

    // Bring window to front
    function bringToFront(window) {
        window.style.zIndex = ++windowZIndex;

        // Update taskbar active states
        document.querySelectorAll('.taskbar-item').forEach(item => {
            item.classList.remove('active');
        });

        const appName = window.getAttribute('data-app');
        const taskbarItem = document.querySelector(`[data-taskbar-app="${appName}"]`);
        if (taskbarItem) {
            taskbarItem.classList.add('active');
        }
    }
    
    // Window controls functionality
    document.addEventListener('click', function(e) {
        // Check if clicked element or its parent is a control
        let control = null;
        if (e.target.classList.contains('ubuntu-control')) {
            control = e.target;
        } else if (e.target.closest('.ubuntu-control')) {
            control = e.target.closest('.ubuntu-control');
        }

        if (control) {
            e.preventDefault();
            e.stopPropagation();

            const window = control.closest('.app-window');
            if (!window) {
                console.error('Window not found for control');
                return;
            }

            const appName = window.getAttribute('data-app');
            const action = control.getAttribute('data-action');

            console.log('Control clicked:', action, 'for app:', appName);

            // Visual feedback
            control.style.transform = 'scale(0.9)';
            setTimeout(() => {
                control.style.transform = 'scale(1)';
            }, 150);

            // Execute action
            switch(action) {
                case 'close':
                    console.log('Closing window:', appName);
                    closeWindow(appName);
                    break;
                case 'minimize':
                    console.log('Minimizing window:', appName);
                    minimizeWindow(appName);
                    break;
                case 'maximize':
                    console.log('Toggling maximize for window:', appName);
                    toggleMaximize(appName);
                    break;
                default:
                    console.error('Unknown action:', action);
            }
        }
    });

    // Taskbar management
    function addToTaskbar(appName, window) {
        const taskbar = document.querySelector('.taskbar-items');
        if (!taskbar) {
            console.error('Taskbar not found');
            return;
        }

        const windowTitle = window.querySelector('.window-title').textContent;

        // Get app icon from dock
        const dockItem = document.querySelector(`[data-view="${appName}"] .dock-icon`);
        const icon = dockItem ? dockItem.textContent : getDefaultIcon(appName);

        const taskbarItem = document.createElement('div');
        taskbarItem.className = 'taskbar-item active';
        taskbarItem.setAttribute('data-taskbar-app', appName);
        taskbarItem.innerHTML = `
            <div class="taskbar-icon">${icon}</div>
            <div class="taskbar-title">${windowTitle}</div>
        `;

        taskbarItem.addEventListener('click', function() {
            const targetWindow = document.getElementById(appName + '-window');
            if (targetWindow.classList.contains('minimized')) {
                // Restore from minimized
                targetWindow.classList.remove('minimized');
                targetWindow.classList.add('active');
                this.classList.add('active');
                bringToFront(targetWindow);
            } else {
                // Minimize if already active
                minimizeWindow(appName);
            }
        });

        taskbar.appendChild(taskbarItem);
    }

    function getDefaultIcon(appName) {
        const iconMap = {
            'home': '🏠',
            'products': '📦',
            'solutions': '🔧',
            'about': 'ℹ️',
            'contact': '📞'
        };
        return iconMap[appName] || '📄';
    }

    function removeFromTaskbar(appName) {
        const taskbarItem = document.querySelector(`[data-taskbar-app="${appName}"]`);
        if (taskbarItem) {
            taskbarItem.remove();
        }
    }

    function updateDockActiveState() {
        // Update dock items to show which apps are open
        document.querySelectorAll('.dock-item').forEach(item => {
            const appName = item.getAttribute('data-view');
            if (activeWindows.has(appName)) {
                item.classList.add('active');
            } else {
                item.classList.remove('active');
            }
        });
    }
    
    // Ubuntu button hover effects
    const ubuntuButtons = document.querySelectorAll('.ubuntu-btn');
    
    ubuntuButtons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });
        
        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
        
        button.addEventListener('click', function() {
            // Click animation
            this.style.transform = 'translateY(0) scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'translateY(-2px) scale(1)';
            }, 150);
        });
    });
    
    // Card hover effects
    const cards = document.querySelectorAll('.ubuntu-card');
    
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.boxShadow = '0 8px 24px rgba(0, 0, 0, 0.15)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)';
        });
    });
    
    // Window dragging functionality
    document.addEventListener('mousedown', function(e) {
        const windowHeader = e.target.closest('.window-header');
        if (windowHeader && !e.target.classList.contains('control')) {
            const window = windowHeader.closest('.app-window');
            if (window.classList.contains('maximized')) return; // Can't drag maximized windows

            draggedWindow = window;
            const rect = window.getBoundingClientRect();
            dragOffset.x = e.clientX - rect.left;
            dragOffset.y = e.clientY - rect.top;

            window.classList.add('dragging');
            bringToFront(window);

            e.preventDefault();
        }
    });

    document.addEventListener('mousemove', function(e) {
        if (draggedWindow) {
            const x = e.clientX - dragOffset.x;
            const y = e.clientY - dragOffset.y;

            // Constrain to viewport
            const maxX = window.innerWidth - draggedWindow.offsetWidth;
            const maxY = window.innerHeight - draggedWindow.offsetHeight;

            draggedWindow.style.left = Math.max(0, Math.min(x, maxX)) + 'px';
            draggedWindow.style.top = Math.max(32, Math.min(y, maxY)) + 'px'; // 32px for top bar
        }
    });

    document.addEventListener('mouseup', function() {
        if (draggedWindow) {
            draggedWindow.classList.remove('dragging');
            draggedWindow = null;
        }
    });

    // Window click to bring to front
    document.addEventListener('mousedown', function(e) {
        const window = e.target.closest('.app-window');
        if (window) {
            bringToFront(window);
        }
    });

    // Mac Dock magnification effect
    const dockItems = document.querySelectorAll('.dock-item');
    const dockContainer = document.querySelector('.dock-container');

    dockItems.forEach((item, index) => {
        item.addEventListener('mouseenter', function() {
            // Magnify current item and adjacent items
            dockItems.forEach((otherItem, otherIndex) => {
                const distance = Math.abs(index - otherIndex);
                let scale = 1;

                if (distance === 0) {
                    scale = 1.4; // Current item
                } else if (distance === 1) {
                    scale = 1.2; // Adjacent items
                } else if (distance === 2) {
                    scale = 1.1; // Next adjacent items
                }

                otherItem.style.transform = `scale(${scale}) translateY(-${(scale - 1) * 20}px)`;
            });
        });
    });

    dockContainer.addEventListener('mouseleave', function() {
        // Reset all items
        dockItems.forEach(item => {
            item.style.transform = 'scale(1) translateY(0)';
        });
    });

    // Activities button functionality - Ubuntu Activities Overview
    const activitiesButton = document.querySelector('.activities-button');
    let isOverviewMode = false;

    activitiesButton.addEventListener('click', function() {
        toggleActivitiesOverview();
    });

    function toggleActivitiesOverview() {
        const desktop = document.querySelector('.ubuntu-desktop');
        const allWindows = document.querySelectorAll('.app-window.active');
        const dock = document.querySelector('.mac-dock');
        const taskbar = document.querySelector('.taskbar');

        isOverviewMode = !isOverviewMode;

        if (isOverviewMode) {
            // Enter overview mode
            activitiesButton.classList.add('active');
            desktop.classList.add('overview-mode');

            // Scale down and arrange windows
            allWindows.forEach((window, index) => {
                window.classList.add('overview-window');
                const row = Math.floor(index / 3);
                const col = index % 3;
                const x = 100 + col * 300;
                const y = 100 + row * 250;

                window.style.transform = `scale(0.4) translate(${x}px, ${y}px)`;
                window.style.transformOrigin = 'top left';
                window.style.zIndex = 1000 + index;

                // Add click handler to exit overview
                window.addEventListener('click', exitOverviewMode);
            });

            // Hide dock and taskbar in overview
            if (dock) dock.style.opacity = '0.3';
            if (taskbar) taskbar.style.opacity = '0.3';

        } else {
            exitOverviewMode();
        }
    }

    function exitOverviewMode() {
        const desktop = document.querySelector('.ubuntu-desktop');
        const allWindows = document.querySelectorAll('.app-window');
        const dock = document.querySelector('.mac-dock');
        const taskbar = document.querySelector('.taskbar');

        isOverviewMode = false;
        activitiesButton.classList.remove('active');
        desktop.classList.remove('overview-mode');

        // Restore windows
        allWindows.forEach(window => {
            window.classList.remove('overview-window');
            window.style.transform = '';
            window.style.transformOrigin = '';
            window.removeEventListener('click', exitOverviewMode);
        });

        // Restore dock and taskbar
        if (dock) dock.style.opacity = '1';
        if (taskbar) taskbar.style.opacity = '1';
    }

    // ESC key to exit overview
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && isOverviewMode) {
            exitOverviewMode();
        }
    });
    
    // System indicators functionality
    const indicators = document.querySelectorAll('.indicator');
    
    indicators.forEach(indicator => {
        indicator.addEventListener('click', function() {
            // Simple notification effect
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 50px;
                right: 20px;
                background: rgba(44, 0, 30, 0.9);
                color: white;
                padding: 12px 16px;
                border-radius: 8px;
                font-size: 14px;
                z-index: 1000;
                animation: slideIn 0.3s ease;
            `;
            
            const messages = {
                '🔊': '音量: 75%',
                '📶': '网络: 已连接',
                '🔋': '电池: 85%',
                '⚙️': '设置面板'
            };
            
            notification.textContent = messages[this.textContent] || '系统通知';
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 2000);
        });
    });
    
    // Add slide-in animation
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
    `;
    document.head.appendChild(style);
    
    // Clock update
    function updateClock() {
        const now = new Date();
        const options = { 
            month: 'long', 
            day: 'numeric',
            hour: '2-digit', 
            minute: '2-digit',
            hour12: false 
        };
        const timeString = now.toLocaleDateString('zh-CN', options);
        const clockElement = document.querySelector('.clock');
        if (clockElement) {
            clockElement.textContent = timeString.replace('年', '年 ');
        }
    }
    
    // Update clock every minute
    updateClock();
    setInterval(updateClock, 60000);
    
    // Smooth scrolling for content areas
    const contentAreas = document.querySelectorAll('.window-content');
    
    contentAreas.forEach(area => {
        area.style.scrollBehavior = 'smooth';
    });
    
    // Add loading animation on page load
    document.body.style.opacity = '0';
    document.body.style.transition = 'opacity 0.5s ease';
    
    setTimeout(() => {
        document.body.style.opacity = '1';
    }, 100);
    
    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Alt + number keys for navigation
        if (e.altKey) {
            const keyMap = {
                '1': 'home',
                '2': 'products', 
                '3': 'solutions',
                '4': 'about',
                '5': 'contact'
            };
            
            const targetView = keyMap[e.key];
            if (targetView) {
                e.preventDefault();
                const targetNav = document.querySelector(`[data-view="${targetView}"]`);
                if (targetNav) {
                    targetNav.click();
                }
            }
        }
    });
    
    // Auto-open home window on load
    setTimeout(() => {
        openWindow('home');
    }, 500);

    console.log('Ubuntu style website loaded successfully! 🚀');
    console.log('Keyboard shortcuts: Alt + 1-5 for navigation');
    console.log('Window controls: Drag to move, click controls to minimize/maximize/close');
});
