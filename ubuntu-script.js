// Ubuntu Style JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Ubuntu Menu functionality
    const appsButton = document.getElementById('apps-btn');
    const ubuntuMenu = document.getElementById('ubuntu-menu');
    const menuSearch = document.getElementById('menu-search');

    // Toggle Ubuntu menu
    appsButton.addEventListener('click', function() {
        ubuntuMenu.classList.toggle('active');
        this.classList.toggle('active');
    });

    // Close menu when clicking outside
    document.addEventListener('click', function(e) {
        if (!ubuntuMenu.contains(e.target) && !appsButton.contains(e.target)) {
            ubuntuMenu.classList.remove('active');
            appsButton.classList.remove('active');
        }
    });

    // Menu search functionality
    menuSearch.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const appItems = document.querySelectorAll('.app-item');

        appItems.forEach(item => {
            const appName = item.querySelector('.app-name').textContent.toLowerCase();
            if (appName.includes(searchTerm)) {
                item.style.display = 'flex';
            } else {
                item.style.display = 'none';
            }
        });
    });

    // Navigation functionality for both Ubuntu menu and Mac dock
    const navItems = document.querySelectorAll('[data-view]');
    const contentViews = document.querySelectorAll('.content-view');

    // Handle navigation clicks
    navItems.forEach(item => {
        item.addEventListener('click', function() {
            const targetView = this.getAttribute('data-view');

            // Remove active class from all nav items
            navItems.forEach(nav => nav.classList.remove('active'));

            // Add active class to clicked item
            this.classList.add('active');

            // Hide all content views
            contentViews.forEach(view => view.classList.remove('active'));

            // Show target view
            const targetElement = document.getElementById(targetView + '-view');
            if (targetElement) {
                targetElement.classList.add('active');
            }

            // Close Ubuntu menu if open
            ubuntuMenu.classList.remove('active');
            appsButton.classList.remove('active');
        });
    });
    
    // Window controls functionality
    const windowControls = document.querySelectorAll('.control');
    
    windowControls.forEach(control => {
        control.addEventListener('click', function() {
            if (this.classList.contains('close')) {
                // Close animation
                this.style.transform = 'scale(0.8)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);
            } else if (this.classList.contains('minimize')) {
                // Minimize animation
                this.style.transform = 'scale(0.8)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);
            } else if (this.classList.contains('maximize')) {
                // Maximize animation
                this.style.transform = 'scale(0.8)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);
            }
        });
    });
    
    // Ubuntu button hover effects
    const ubuntuButtons = document.querySelectorAll('.ubuntu-btn');
    
    ubuntuButtons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });
        
        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
        
        button.addEventListener('click', function() {
            // Click animation
            this.style.transform = 'translateY(0) scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'translateY(-2px) scale(1)';
            }, 150);
        });
    });
    
    // Card hover effects
    const cards = document.querySelectorAll('.ubuntu-card');
    
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.boxShadow = '0 8px 24px rgba(0, 0, 0, 0.15)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)';
        });
    });
    
    // Mac Dock magnification effect
    const dockItems = document.querySelectorAll('.dock-item');
    const dockContainer = document.querySelector('.dock-container');

    dockItems.forEach((item, index) => {
        item.addEventListener('mouseenter', function() {
            // Magnify current item and adjacent items
            dockItems.forEach((otherItem, otherIndex) => {
                const distance = Math.abs(index - otherIndex);
                let scale = 1;

                if (distance === 0) {
                    scale = 1.4; // Current item
                } else if (distance === 1) {
                    scale = 1.2; // Adjacent items
                } else if (distance === 2) {
                    scale = 1.1; // Next adjacent items
                }

                otherItem.style.transform = `scale(${scale}) translateY(-${(scale - 1) * 20}px)`;
            });
        });
    });

    dockContainer.addEventListener('mouseleave', function() {
        // Reset all items
        dockItems.forEach(item => {
            item.style.transform = 'scale(1) translateY(0)';
        });
    });

    // Activities button functionality
    const activitiesButton = document.querySelector('.activities-button');

    activitiesButton.addEventListener('click', function() {
        // Toggle overview effect
        const desktop = document.querySelector('.ubuntu-desktop');
        desktop.style.transform = desktop.style.transform === 'scale(0.8)' ? 'scale(1)' : 'scale(0.8)';
        desktop.style.transition = 'transform 0.3s ease';

        setTimeout(() => {
            desktop.style.transform = 'scale(1)';
        }, 1000);
    });
    
    // System indicators functionality
    const indicators = document.querySelectorAll('.indicator');
    
    indicators.forEach(indicator => {
        indicator.addEventListener('click', function() {
            // Simple notification effect
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 50px;
                right: 20px;
                background: rgba(44, 0, 30, 0.9);
                color: white;
                padding: 12px 16px;
                border-radius: 8px;
                font-size: 14px;
                z-index: 1000;
                animation: slideIn 0.3s ease;
            `;
            
            const messages = {
                '🔊': '音量: 75%',
                '📶': '网络: 已连接',
                '🔋': '电池: 85%',
                '⚙️': '设置面板'
            };
            
            notification.textContent = messages[this.textContent] || '系统通知';
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 2000);
        });
    });
    
    // Add slide-in animation
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
    `;
    document.head.appendChild(style);
    
    // Clock update
    function updateClock() {
        const now = new Date();
        const options = { 
            month: 'long', 
            day: 'numeric',
            hour: '2-digit', 
            minute: '2-digit',
            hour12: false 
        };
        const timeString = now.toLocaleDateString('zh-CN', options);
        const clockElement = document.querySelector('.clock');
        if (clockElement) {
            clockElement.textContent = timeString.replace('年', '年 ');
        }
    }
    
    // Update clock every minute
    updateClock();
    setInterval(updateClock, 60000);
    
    // Smooth scrolling for content areas
    const contentAreas = document.querySelectorAll('.window-content');
    
    contentAreas.forEach(area => {
        area.style.scrollBehavior = 'smooth';
    });
    
    // Add loading animation on page load
    document.body.style.opacity = '0';
    document.body.style.transition = 'opacity 0.5s ease';
    
    setTimeout(() => {
        document.body.style.opacity = '1';
    }, 100);
    
    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Alt + number keys for navigation
        if (e.altKey) {
            const keyMap = {
                '1': 'home',
                '2': 'products', 
                '3': 'solutions',
                '4': 'about',
                '5': 'contact'
            };
            
            const targetView = keyMap[e.key];
            if (targetView) {
                e.preventDefault();
                const targetNav = document.querySelector(`[data-view="${targetView}"]`);
                if (targetNav) {
                    targetNav.click();
                }
            }
        }
    });
    
    console.log('Ubuntu style website loaded successfully! 🚀');
    console.log('Keyboard shortcuts: Alt + 1-5 for navigation');
});
